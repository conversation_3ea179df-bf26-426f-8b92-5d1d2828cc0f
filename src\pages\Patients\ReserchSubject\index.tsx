import React, { useEffect, useState } from 'react';
import locale from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import { Form, Input, Select, DatePicker, Button, ConfigProvider, Space, message, Row, Col, Upload, Modal } from 'antd';
import 'dayjs/locale/zh-cn';
import { UploadOutlined } from '@ant-design/icons';
import { genderMap, doctorMap, patientGroupMap } from '@/utils/data';
import { useDispatch } from 'umi';
import storeUtil from '@/utils/storeUtil';

declare const GLPT_API: string;

dayjs.locale('zh-cn');


interface CreateSubjectProps {
    onFinish?: (values: any) => void;
    onCancel?: () => void;
    projectId?: string;
    projectName?: string;
    mode?: 'create' | 'edit';
    initialValues?: any;
    dispatch?: any; // 添加 dispatch 属性
  }

const { Option } = Select;

const ReserchSubject: React.FC<CreateSubjectProps> = (props) => {
    const {
        onFinish,
        onCancel,
        projectId,
        projectName,
        mode = 'create',
        initialValues,
        dispatch,
      } = props;
      
    
      const [form] = Form.useForm();
      const [consentLetterUrl, setConsentLetterUrl] = useState<string>('');

    // 添加调试信息
    useEffect(() => {
        console.log('ReserchSubject props debug:', {
            projectId,
            projectName,
            mode,
            initialValues
        });
    }, [projectId, projectName, mode, initialValues]);

    // 当组件挂载或projectId/projectName变化时重置表单
    useEffect(() => {
        // 重置表单
        form.resetFields();

        // 如果有项目ID和名称，自动填充到表单中
        if (projectId && projectName) {
            form.setFieldsValue({
                projectId,
                projectName
            });
        }

        // 如果是编辑模式且有初始值，填充表单
        if (mode === 'edit' && initialValues) {
            form.setFieldsValue(initialValues);
            // 如果编辑模式下有 consentLetter，也设置一下
            if (initialValues.consentLetter) {
                setConsentLetterUrl(initialValues.consentLetter);
            }
        }

        // 重置状态
        if (mode === 'create') { // 创建模式下清空 consentLetterUrl
            setConsentLetterUrl('');
        }
    }, [projectId, projectName, form, mode, initialValues]);

    const getProjectIdFromName = (name: string): number => {
        // 根据项目名称获取项目ID
        if (name && name.includes('基于电子化患者报告结局的肺癌患者情绪压力及症状负担的相关性研究')) {
            return 1;
        } else if (name && name.includes('同济基于ePRO肺癌根治性同步放化疗患者症状负担和症状群研究项目')) {
            return 2;
        }
        return 1;
    };

    // 获取实际的项目ID，优先使用传入的projectId，如果没有则从projectName推断
    const getActualProjectId = (): string => {
        if (projectId) {
            return projectId;
        }
        if (projectName) {
            return getProjectIdFromName(projectName).toString();
        }
        return '1'; // 默认项目1
    };

    const actualProjectId = getActualProjectId();
    console.log('actualProjectId:', actualProjectId, 'original projectId:', projectId);

    // 修改后的handleFinish函数，直接处理表单提交
    const handleFinish = (values: any) => {
        // 转换表单数据为API所需格式
        let formData: any = {
            recordId: Number(values.recordId),
            name: values.name,
            phone: values.phone,
            project: getProjectIdFromName(values.projectName),
            projectGroup: values.projectGroup || '默认组',
            patientGroup: values.patientGroup, // 直接使用Select的数字值
            enrollmentNumber: Number(values.enrollmentNumber) || 1,
            gender: genderMap[values.sex || '未知'] || 0,
            age: values.age ? Number(values.age) : undefined,
            department: values.department || undefined,
            bedNumber: values.bed || undefined,
            doctor: doctorMap[values.doctor || '未知'] || 0,
            remark: values.remark || undefined,
            consentLetter: consentLetterUrl || undefined, // 使用 state 中的 consentLetterUrl
            contactName: values.contactName || undefined,
            contactPhone: values.contactPhone || undefined,
            extraPhone: values.extraPhone ? [values.extraPhone] : undefined,
            isDelete: 0,
        };

        // 根据项目类型处理不同的日期字段
        if (actualProjectId === '1') {
            // 项目1：只处理确诊日期
            if (values.diagnosisDate) {
                formData.diagnosisDate = dayjs(values.diagnosisDate).format('YYYY-MM-DD HH:mm:ss');
            }
        } else if (actualProjectId === '2') {
            // 项目2：只处理放疗日期
            if (values.radiotherapyStartDate) {
                formData.radiotherapyStartDate = dayjs(values.radiotherapyStartDate).format('YYYY-MM-DD HH:mm:ss');
            }
            if (values.radiotherapyEndDate) {
                formData.radiotherapyEndDate = dayjs(values.radiotherapyEndDate).format('YYYY-MM-DD HH:mm:ss');
            }
        }

        // 过滤掉 undefined 和空字符串的字段
        Object.keys(formData).forEach(key => {
            if (
                formData[key] === undefined ||
                formData[key] === '' ||
                formData[key] === null
            ) {
                delete formData[key];
            }
        });

        // 直接调用父组件传递的 onFinish 方法
        if (onFinish) {
            onFinish(formData);
        }
    };

   console.log('actualProjectId:', actualProjectId);

    // 直接修改，不需要签名
    const handleUpdate = () => {
        form.validateFields().then(values => {
            // 确保有 id 字段，这是更新接口必须的
            if (!initialValues?.id && mode === 'edit') {
                message.error('缺少患者ID，无法更新');
                return;
            }

            const formData: any = {
                id: initialValues?.id, // 添加 id 字段，这是更新接口必须的
                recordId: Number(values.recordId),
                name: values.name,
                phone: values.phone,
                project: getProjectIdFromName(values.projectName),
                projectGroup: values.projectGroup || '默认组',
                patientGroup: values.patientGroup, // 直接使用Select的数字值
                enrollmentNumber: Number(values.enrollmentNumber) || 1,
                gender: genderMap[values.sex || '未知'] || 0,
                age: values.age ? Number(values.age) : undefined,
                department: values.department || undefined,
                bedNumber: values.bed || undefined,
                doctor: doctorMap[values.doctor || '未知'] || 0,
                remark: values.remark || undefined,
                consentLetter: consentLetterUrl || values.consentLetter || undefined,
                contactName: values.contactName || undefined,
                contactPhone: values.contactPhone || undefined,
                nurse: values.nurse || null, // 添加护士字段
                isDelete: 0, // 默认不删除
            };

            // 根据项目类型处理不同的日期字段
            if (actualProjectId === '1') {
                // 项目1：只处理确诊日期
                if (values.diagnosisDate) {
                    formData.diagnosisDate = dayjs(values.diagnosisDate).format('YYYY-MM-DD HH:mm:ss');
                } else {
                    formData.diagnosisDate = null;
                }
            } else if (actualProjectId === '2') {
                // 项目2：只处理放疗日期
                if (values.radiotherapyStartDate) {
                    formData.radiotherapyStartDate = dayjs(values.radiotherapyStartDate).format('YYYY-MM-DD HH:mm:ss');
                } else {
                    formData.radiotherapyStartDate = null;
                }
                if (values.radiotherapyEndDate) {
                    formData.radiotherapyEndDate = dayjs(values.radiotherapyEndDate).format('YYYY-MM-DD HH:mm:ss');
                } else {
                    formData.radiotherapyEndDate = null;
                }
            }
            console.log('diagnosisDate:', values.diagnosisDate);
            // 过滤掉 undefined 字段
            Object.keys(formData).forEach(key => {
                if (formData[key] === undefined) {
                    delete formData[key];
                }
            });

            if (mode === 'edit' && dispatch) {
                // 调用 model 的 updatePatient 方法
                dispatch({
                    type: 'patients/updatePatient',
                    payload: formData,
                }).then((result: any) => {
                    // 检查更新是否成功
                    if (result && result.success) {
                        message.success('研究对象信息更新成功');
                        // 调用父组件的 onFinish 回调通知更新成功
                        if (onFinish) {
                            onFinish(formData);
                        }
                    } else {
                        message.error(result?.message || '更新失败，请重试');
                    }
                }).catch((error: any) => {
                    console.error('更新研究对象出错:', error);
                    message.error('更新失败，请检查网络连接');
                });
            } else if (onFinish) {
                // 如果没有 dispatch 或不是编辑模式，直接调用 onFinish
                onFinish(formData);
            }
        }).catch(error => {
            console.error('表单验证错误:', error);
        });
    };

    // 处理删除患者
    const handleDelete = () => {
        if (!initialValues?.id) {
            message.error('缺少患者ID，无法删除');
            return;
        }

        Modal.confirm({
            title: '确认删除',
            content: `确定要删除患者 "${initialValues.name}" 吗？此操作不可恢复。`,
            okText: '确认删除',
            cancelText: '取消',
            okType: 'danger',
            okButtonProps: {
                style: {
                    backgroundColor: '#ff4d4f',
                    borderColor: '#ff4d4f',
                    color: '#fff',
                }
            },
            onOk: async () => {
                if (dispatch) {
                    try {
                        const result = await dispatch({
                            type: 'patients/deletePatient',
                            payload: {
                                id: initialValues.id,
                            },
                        });

                        if (result && result.success) {
                            // 调用父组件的回调，关闭Modal并刷新列表
                            if (onFinish) {
                                onFinish({ deleted: true });
                            }
                        } else {
                            message.error(result?.message || '删除失败，请重试');
                        }
                    } catch (error) {
                        console.error('删除患者出错:', error);
                        message.error('删除失败，请检查网络连接');
                    }
                } else {
                    message.error('无法删除，请重试');
                }
            },
        });
    };

    // 处理同意书上传
    const handleConsentLetterUpload = (info: any) => {
        if (info.file.status === 'done') {
            setConsentLetterUrl(info.file.response.url || 'http://example.com/consent.pdf');
            message.success(`${info.file.name} 上传成功`);
        } else if (info.file.status === 'error') {
            message.error(`${info.file.name} 上传失败`);
        }
    };

    return (
        <ConfigProvider
            locale={locale}
            theme={{
                components: {
                    Input: {
                        activeBorderColor: '#39bbdb',
                        hoverBorderColor: '#39bbdb',
                        borderRadius: 4,
                        activeShadow: '0 0 0 2px rgba(24, 144, 255, 0.2)',
                    },
                    Select: {
                        activeBorderColor: '#39bbdb',
                        hoverBorderColor: '#39bbdb',
                        borderRadius: 4,
                        controlOutline: '0 0 0 2px rgba(24, 144, 255, 0.2)',
                        optionSelectedBg: '#e6f7ff',
                    },
                    DatePicker: {
                        activeBorderColor: '#39bbdb',
                        hoverBorderColor: '#39bbdb',
                        borderRadius: 4,
                        activeShadow: '0 0 0 2px rgba(24, 144, 255, 0.2)'
                    },
                    Button: {
                        colorPrimary: '#39bbdb',
                    },
                },
            }}
        >
            <Form
                form={form}
                onFinish={handleFinish}
                layout="vertical"
            >
                {/* 项目ID（隐藏） */}
                <Form.Item
                    name="project"
                    label="项目ID"
                    hidden
                >
                    <Input />
                </Form.Item>

                {/* 项目名称（单独一行） */}
                <Form.Item
                    name="projectName"
                    label="项目名称"
                >
                    <Input disabled />
                </Form.Item>

                {/* 以下表单项使用两列布局 */}
                <Row gutter={16}>
                    <Col span={12}>
                        <Form.Item
                            name="recordId"
                            label="研究对象病案号"
                            rules={[{ required: true, message: '请输入研究对象病案号' }]}
                        >
                            <Input placeholder="请输入研究对象病案号" />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            name="name"
                            label="研究对象姓名"
                            rules={[{ required: true, message: '请输入研究对象姓名' }]}
                        >
                            <Input placeholder="请输入研究对象姓名" />
                        </Form.Item>
                    </Col>
                </Row>

                <Row gutter={16}>
                    <Col span={12}>
                        <Form.Item
                            label="进组次数"
                            name="enrollmentNumber"
                            initialValue={1}
                            rules={[{ required: true, message: '请输入进组次数' }]}
                        >
                            <Input type="number" min={1} placeholder="请输入进组次数" />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            name="phone"
                            label="研究对象电话"
                            rules={[
                                { required: true, message: '请输入研究对象电话' },
                                {
                                    pattern: /^(1[3-9]\d{9}|(\d{3,4}-\d{7,8}))$/,
                                    message: '请输入正确的电话号码格式'
                                }
                            ]}
                        >
                            <Input placeholder="请输入研究对象电话" />
                        </Form.Item>
                    </Col>
                </Row>

                <Row gutter={16}>
                    {actualProjectId === '1' ? (
                        <>
                            <Col span={12}>
                                <Form.Item
                                    label="项目分组"
                                    name="projectGroup"
                                    initialValue="默认组"
                                    rules={[{ required: true, message: '请选择项目分组' }]}
                                >
                                    <Select placeholder="请选择">
                                        {['默认组'].map(item => (
                                            <Option key={item} value={item}>{item}</Option>
                                        ))}
                                    </Select>
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item
                                    label="确诊日期"
                                    name="diagnosisDate"
                                >
                                    <DatePicker style={{ width: '100%' }} />
                                </Form.Item>
                            </Col>
                        </>
                    ) : (
                        <>
                            <Col span={12}>
                                <Form.Item
                                    label="放疗开始日期"
                                    name="radiotherapyStartDate"
                                >
                                    <DatePicker style={{ width: '100%' }} />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item
                                    label="放疗结束日期"
                                    name="radiotherapyEndDate"
                                >
                                    <DatePicker style={{ width: '100%' }} />
                                </Form.Item>
                            </Col>
                        </>
                    )}
                </Row>

                <Row gutter={16}>
                    <Col span={12}>
                        <Form.Item
                            label="性别"
                            name="sex"
                        >
                            <Select placeholder="请选择">
                                {['男', '女'].map(item => (
                                    <Option key={item} value={item}>{item}</Option>
                                ))}
                            </Select>
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            label="年龄"
                            name="age"
                        >
                            <Input placeholder="请输入年龄" />
                        </Form.Item>
                    </Col>
                </Row>

                <Row gutter={16}>
                    <Col span={12}>
                        <Form.Item
                            label="科室"
                            name="department"
                        >
                            <Input placeholder="请输入科室" />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            label="床位号"
                            name="bed"
                        >
                            <Input placeholder="请输入床位号" />
                        </Form.Item>
                    </Col>
                </Row>

                <Row gutter={16}>
                    <Col span={12}>
                        <Form.Item
                            label="主管医生"
                            name="doctor"
                        >
                            <Select placeholder="请选择">
                                {['蒋继宗', '吴莹莹', '袁逊', '李杨', '王静-张路教授',
                                    '周磊-褚倩教授', '朱颖莺-夏曙教授',
                                    '杨昕-夏曙教授', '肖晓光', '张鹏'].map(item => (
                                        <Option key={item} value={item}>{item}</Option>
                                    ))}
                            </Select>
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            label="患者组别"
                            name="patientGroup"
                            initialValue={1}
                        >
                            <Select placeholder="请选择患者组别">
                                <Option value={1}>默认访问组</Option>
                                <Option value={0}>未分组</Option>
                                <Option value={2}>周三分组</Option>
                                <Option value={3}>周五分组</Option>
                            </Select>
                        </Form.Item>
                    </Col>
                </Row>

                <Row gutter={16}>
                    <Col span={12}>
                        <Form.Item
                            label="备注"
                            name="remark"
                        >
                            <Input placeholder="请输入备注" />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            label="联系人姓名"
                            name="contactName"
                        >
                            <Input placeholder="请输入联系人姓名" />
                        </Form.Item>
                    </Col>
                </Row>

                <Row gutter={16}>
                    <Col span={12}>
                        <Form.Item
                            label="联系人电话"
                            name="contactPhone"
                        >
                            <Input placeholder="请输入联系人电话" />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            label="额外联系电话"
                            name="extraPhone"
                        >
                            <Input placeholder="请输入额外联系电话" />
                        </Form.Item>
                    </Col>
                </Row>

                <Row gutter={16}>
                    <Col span={12}>
                        <Form.Item label="上传纸质同意书">
                            <Upload
                                name="consentLetter"
                                action="/api/upload"
                                onChange={handleConsentLetterUpload}
                            >
                                <Button icon={<UploadOutlined />}>
                                    上传
                                </Button>
                            </Upload>
                        </Form.Item>
                    </Col>
                </Row>

                <Form.Item>
                <Space style={{ display: 'flex', justifyContent: 'flex-end', width: '100%' }}>
                    {mode === 'create' && (
                        <Button onClick={onCancel}>取消</Button>
                    )}
                    {mode === 'edit' && initialValues?.id && (
                        <>
                            <Button
                                danger
                                onClick={handleDelete}
                                style={{
                                    backgroundColor: '#ff4d4f',
                                    borderColor: '#ff4d4f',
                                    color: '#fff',
                                    cursor: 'pointer',
                                    transition: 'none',
                                    boxShadow: 'none'
                                }}
                            >
                                删除
                            </Button>
                            <Button
                                type="default"
                                onClick={async () => {
                                    try {
                                        // 获取token
                                        const tokenData = storeUtil?.get('token');
                                        const token = tokenData && tokenData.status === storeUtil?.status?.SUCCESS ? tokenData.value : null;
                                        
                                        if (!token) {
                                            message.error('未找到有效的登录凭证，请重新登录');
                                            return;
                                        }
                                        
                                        message.loading('正在加载PDF预览...', 0);
                                        
                                        // 使用fetch发起请求获取PDF
                                        const response = await fetch(`${GLPT_API}/file/preview?id=${initialValues.id}`, {
                                            method: 'GET',
                                            headers: {
                                                'Authorization': token,
                                            },
                                        });
                                        
                                        message.destroy(); // 清除loading消息
                                        
                                        if (!response.ok) {
                                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                                        }
                                        
                                        // 检查响应类型
                                        const contentType = response.headers.get('content-type');
                                        if (contentType && contentType.includes('application/pdf')) {
                                            // 如果是PDF文件，创建blob URL并打开
                                            const blob = await response.blob();
                                            const url = window.URL.createObjectURL(blob);
                                            const newWindow = window.open(url, '_blank');
                                            if (!newWindow) {
                                                message.error('无法打开新窗口，请检查浏览器设置');
                                            }
                                        } else if (contentType && contentType.includes('application/json')) {
                                            // 如果是JSON响应，检查错误信息
                                            const result = await response.json();
                                            if (result.code !== 0) {
                                                message.error(result.message || 'PDF预览失败');
                                            } else {
                                                message.error('服务器返回的不是PDF文件');
                                            }
                                        }
                                        else {
                                            // 处理其他类型的响应或空响应
                                            const textResponse = await response.text();
                                            console.error('预览PDF时的意外响应:', textResponse);
                                            message.error(textResponse || 'PDF预览失败，服务器返回未知响应');
                                        }
                                    } catch (error) {
                                        message.destroy(); // 确保清除loading消息
                                        console.error('预览PDF失败:', error);
                                        message.error('PDF预览失败，请重试');
                                    }
                                }}
                                className="custom_toolbar_buttom"
                                style={{
                                    border: '1px solid #d9d9d9',
                                    backgroundColor: '#fff',
                                    color: 'rgba(0, 0, 0, 0.88)',
                                    cursor: 'pointer',
                                    transition: 'none',
                                    boxShadow: 'none'
                                }}
                            >
                                预览PDF
                            </Button>
                        </>
                    )}
                    <Button
                        type="primary"
                        onClick={() => {
                            if (mode === 'create') {
                                form.submit(); //触发表单的 onFinish，即这里的 handleFinish
                            } else {
                                handleUpdate(); // 编辑模式调用 handleUpdate
                            }
                        }}
                        className="custom_toolbar_buttom"
                        style={{
                            border: '1px solid #39bbdb',
                            backgroundColor: '#39bbdb',
                            color: '#fff',
                            cursor: 'pointer',
                            transition: 'none',
                            boxShadow: 'none'
                        }}

                    >
                        {mode === 'create' ? '创建' : '保存'}
                    </Button>
                </Space>
            </Form.Item>
        </Form>
        </ConfigProvider >
    );
};

export default ReserchSubject;