import React, { useState } from 'react';
import { Form, Card, Radio, Input, Button, message, Divider, Row, Col, InputNumber, ConfigProvider } from 'antd';
import type { RadioChangeEvent } from 'antd';

interface QuestionnaireProps {
  patientId: string;
  patientName: string;
  projectType?: string;
  isReadOnly?: boolean;
  initialValues?: any;
  onSubmit?: (values: any) => void;
  enrollmentNumber?: number;
  cycleNumber?: number;
  dispatch?: any;
}

// MDASI-LC 公共组件
const MdasiLcComponent: React.FC<{ isReadOnly?: boolean }> = ({ isReadOnly = false }) => {
  // MDASI-LC 症状列表
  const mdasiSymptoms = [
    '您疼痛最严重的程度为?',
    '您疲劳 (乏力) 最严重的程度为?',
    '您恶心最严重的程度为?',
    '您睡眠不安最严重的程度为?',
    '您最苦恼的程度为?',
    '您气短最严重的程度为?',
    '您健忘最严重的程度为?',
    '您胃口最差的程度为?',
    '您瞌睡(昏昏欲睡) 最严重的程度为?',
    '您口干最严重的程度为?',
    '您悲伤感 最严重的程度为?',
    '您呕吐最严重的程度为?',
    '您麻木感 最严重的程度为?',
    '您咳嗽最严重的程度为？',
    '您便秘最严重的程度为？',
    '您嗓子疼最严重的程度为？'
  ];

  // MDASI-LC 干扰项目
  const mdasiInterference = [
    '一般活动？',
    '情绪？',
    '工作（包括家务劳动）?',
    '与他人的关系？',
    '走路？',
    '生活乐趣？'
  ];

  return (
    <Card title="M.D. Anderson 症状调查主要项目 – Lung Cancer (MDASI-LC)" style={{ marginBottom: '24px' }}>
      <div style={{ marginBottom: '16px', fontSize: '16px', fontWeight: 'bold' }}>
        第一部分：您的症状有多严重?
      </div>
      <div style={{ marginBottom: '16px', color: '#666' }}>
        癌症患者常有疾病本身或治疗相关的各种症状。我们想知道您在过去的 24 小时中下列症状的严重程度。
        请将下列每一项从0 (无症状) 至 10 (能想象的最严重程度) 之间圈一数字以表示症状的严重度。
      </div>

      <div style={{ marginBottom: '32px' }}>
        <table style={{ 
          width: '100%', 
          borderCollapse: 'collapse',
          border: '1px solid #d9d9d9',
          tableLayout: 'fixed'
        }}>
          <thead>
            <tr style={{ backgroundColor: '#fafafa' }}>
              <th style={{ 
                border: '1px solid #d9d9d9', 
                padding: '12px 8px',
                textAlign: 'center',
                fontWeight: 'bold',
                width: '25%'
              }}>
                症状项目
              </th>
              <th style={{ 
                border: '1px solid #d9d9d9', 
                padding: '12px 4px',
                textAlign: 'center',
                fontWeight: 'bold',
                width: '6.8%'
              }}>
                0<br/>无症状
              </th>
              {Array.from({ length: 9 }, (_, i) => (
                <th key={i + 1} style={{ 
                  border: '1px solid #d9d9d9', 
                  padding: '12px 4px',
                  textAlign: 'center',
                  fontWeight: 'bold',
                  width: '6.8%'
                }}>
                  {i + 1}
                </th>
              ))}
              <th style={{ 
                border: '1px solid #d9d9d9', 
                padding: '12px 4px',
                textAlign: 'center',
                fontWeight: 'bold',
                width: '6.8%'
              }}>
                10<br/>最严重
              </th>
            </tr>
          </thead>
          <tbody>
            {mdasiSymptoms.map((symptom, index) => (
              <tr key={`symptom_${index}`}>
                <td style={{ 
                  border: '1px solid #d9d9d9', 
                  padding: '12px',
                  textAlign: 'left',
                  verticalAlign: 'middle'
                }}>
                  <span style={{ color: '#ff4d4f', marginRight: '4px' }}>*</span>
                  {index + 1}. {symptom}
                </td>
                <td colSpan={11} style={{ 
                  border: '1px solid #d9d9d9', 
                  padding: '0'
                }}>
                  <Form.Item
                    name={`symptom_${index + 1}`}
                    rules={[{ required: true, message: '请选择症状程度' }]}
                    style={{ margin: 0 }}
                  >
                    <Radio.Group style={{ width: '100%', display: 'grid', gridTemplateColumns: 'repeat(11, 1fr)' }}>
                      {Array.from({ length: 11 }, (_, score) => (
                        <div key={score} style={{ 
                          textAlign: 'center',
                          padding: '8px 4px',
                          borderRight: score < 10 ? '1px solid #d9d9d9' : 'none',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          minHeight: '40px'
                        }}>
                          <Radio value={score} />
                        </div>
                      ))}
                    </Radio.Group>
                  </Form.Item>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <Divider />

      <div style={{ marginBottom: '16px', fontSize: '16px', fontWeight: 'bold' }}>
        第二部分：您的症状妨碍您生活的程度?
      </div>
      <div style={{ marginBottom: '16px', color: '#666' }}>
        症状常常干扰我们的感受和功能．我们想知道在过去的 24 小时中症状干扰您下列各项活动的严重程度。
      </div>

      <div style={{ marginBottom: '32px' }}>
        <table style={{ 
          width: '100%', 
          borderCollapse: 'collapse',
          border: '1px solid #d9d9d9',
          tableLayout: 'fixed'
        }}>
          <thead>
            <tr style={{ backgroundColor: '#fafafa' }}>
              <th style={{ 
                border: '1px solid #d9d9d9', 
                padding: '12px 8px',
                textAlign: 'center',
                fontWeight: 'bold',
                width: '25%'
              }}>
                干扰项目
              </th>
              <th style={{ 
                border: '1px solid #d9d9d9', 
                padding: '12px 4px',
                textAlign: 'center',
                fontWeight: 'bold',
                width: '6.8%'
              }}>
                0<br/>无干扰
              </th>
              {Array.from({ length: 9 }, (_, i) => (
                <th key={i + 1} style={{ 
                  border: '1px solid #d9d9d9', 
                  padding: '12px 4px',
                  textAlign: 'center',
                  fontWeight: 'bold',
                  width: '6.8%'
                }}>
                  {i + 1}
                </th>
              ))}
              <th style={{ 
                border: '1px solid #d9d9d9', 
                padding: '12px 4px',
                textAlign: 'center',
                fontWeight: 'bold',
                width: '6.8%'
              }}>
                10<br/>完全干扰
              </th>
            </tr>
          </thead>
          <tbody>
            {mdasiInterference.map((interference, index) => (
              <tr key={`interference_${index}`}>
                <td style={{ 
                  border: '1px solid #d9d9d9', 
                  padding: '12px',
                  textAlign: 'left',
                  verticalAlign: 'middle'
                }}>
                  <span style={{ color: '#ff4d4f', marginRight: '4px' }}>*</span>
                  {index + 17}. {interference}
                </td>
                <td colSpan={11} style={{ 
                  border: '1px solid #d9d9d9', 
                  padding: '0'
                }}>
                  <Form.Item
                    name={`interference_${index + 17}`}
                    rules={[{ required: true, message: '请选择干扰程度' }]}
                    style={{ margin: 0 }}
                  >
                    <Radio.Group style={{ width: '100%', display: 'grid', gridTemplateColumns: 'repeat(11, 1fr)' }}>
                      {Array.from({ length: 11 }, (_, score) => (
                        <div key={score} style={{ 
                          textAlign: 'center',
                          padding: '8px 4px',
                          borderRight: score < 10 ? '1px solid #d9d9d9' : 'none',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          minHeight: '40px'
                        }}>
                          <Radio value={score} />
                        </div>
                      ))}
                    </Radio.Group>
                  </Form.Item>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <Form.Item
        name="otherSymptoms"
        label="您还有其他症状吗，如有，请补充"
      >
        <Input.TextArea 
          rows={3} 
          placeholder="请描述其他症状..."
        />
      </Form.Item>
    </Card>
  );
};

// 项目1问卷组件
const Project1Questionnaire: React.FC<QuestionnaireProps> = ({ 
  patientId, 
  patientName, 
  isReadOnly = false, 
  initialValues = {}, 
  onSubmit,
  enrollmentNumber,
  cycleNumber,
  dispatch 
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [depressionScore, setDepressionScore] = useState(0);
  const [anxietyScore, setAnxietyScore] = useState(0);

  // EQ-5D 维度
  const eq5dDimensions = [
    { key: 'mobility', label: '行动能力（MO）' },
    { key: 'selfCare', label: '自我照顾能力（SC）' },
    { key: 'usualActivities', label: '日常活动能力（UA）' },
    { key: 'painDiscomfort', label: '疼痛/不适' },
    { key: 'anxietyDepression', label: '焦虑/抑郁' }
  ];

  const eq5dOptions = [
    { value: 1, label: '没有任何困难' },
    { value: 2, label: '有一点困难' },
    { value: 3, label: '中等困难' },
    { value: 4, label: '严重困难' },
    { value: 5, label: '极度困难' }
  ];

  // 抑郁症状问题
  const depressionQuestions = [
    '做什么事都没兴趣，没意思',
    '感到心情低落，抑郁，没希望',
    '入睡困难，总是醒着，或睡得太多嗜睡',
    '常感到很疲倦，没劲',
    '口味不好，或吃的太多',
    '自己对自己不满，觉得自己是个失败者，或让家人丢脸了',
    '无法集中精力，即便是读报纸或看电视时，记忆力下降',
    '行动或说话缓慢到引起人们的注意，或刚好相反，坐卧不安，烦躁易怒易怒，到处走动',
    '有不如一死了之的念头，或想怎样伤害自己一下'
  ];

  const depressionOptions = [
    { value: 0, label: '没有' },
    { value: 1, label: '有几天' },
    { value: 2, label: '一半以上时间' },
    { value: 3, label: '几乎天天' }
  ];

  // 焦虑症状问题
  const anxietyQuestions = [
    '感觉紧张，焦虑或急切',
    '不能够停止或控制担忧',
    '对各种各样的事情担忧过多',
    '很难放松下来',
    '由于不安而无法静坐',
    '变得容易烦恼或急躁',
    '感到似乎将有可怕的事情发生而害怕'
  ];

  const anxietyOptions = [
    { value: 0, label: '完全不会' },
    { value: 1, label: '好几天' },
    { value: 2, label: '超过一周' },
    { value: 3, label: '几乎每天' }
  ];

  const calculateScores = (values: any) => {
    let newDepressionScore = depressionQuestions.reduce((total, _, index) => {
      const value = values[`depression_${index + 1}`];
      return total + (typeof value === 'number' ? value : 0);
    }, 0);

    const depressionImpactValue = values.depressionImpact;
    if (typeof depressionImpactValue === 'number') {
      newDepressionScore += depressionImpactValue;
    }
    setDepressionScore(newDepressionScore);

    const newAnxietyScore = anxietyQuestions.reduce((total, _, index) => {
      const value = values[`anxiety_${index + 1}`];
      return total + (typeof value === 'number' ? value : 0);
    }, 0);
    setAnxietyScore(newAnxietyScore);
  };

  // 设置初始值
  React.useEffect(() => {
    if (initialValues && Object.keys(initialValues).length > 0) {
      form.setFieldsValue(initialValues);
      calculateScores(initialValues);
    }
  }, [initialValues, form]);

  const handleValuesChange = (_: any, allValues: any) => {
    calculateScores(allValues);
  };

  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      if (!dispatch) {
        message.error('提交功能异常，请联系管理员');
        setLoading(false);
        return;
      }

      const params: any = {
        patientId: parseInt(patientId, 10),
        enrollmentNumber,
        cycleNumber,
        q23: values.otherSymptoms,
        q24: values.eq5d_mobility,
        q25: values.eq5d_selfCare,
        q26: values.eq5d_usualActivities,
        q27: values.eq5d_painDiscomfort,
        q28: values.eq5d_anxietyDepression,
        q29: values.healthScore,
        q39: values.depressionImpact,
        q40: depressionScore,
        q48: anxietyScore,
      };

      for (let i = 1; i <= 16; i++) {
        params[`q${i}`] = values[`symptom_${i}`];
      }
      for (let i = 1; i <= 6; i++) {
        params[`q${i + 16}`] = values[`interference_${i + 16}`];
      }
      for (let i = 1; i <= 9; i++) {
        params[`q${i + 29}`] = values[`depression_${i}`];
      }
      for (let i = 1; i <= 7; i++) {
        params[`q${i + 40}`] = values[`anxiety_${i}`];
      }
      
      const result = await dispatch({
        type: 'patients/answerProjectEmo',
        payload: params,
      });

      if (result && result.success) {
        if (onSubmit) {
          onSubmit(values);
        }
      }
      // 错误消息已在模型中处理
    } catch (error) {
      console.error('提交失败:', error);
      message.error('提交失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '20px' }}>
      <div style={{ 
        marginBottom: '24px', 
        fontSize: '20px', 
        fontWeight: 'bold',
        color: '#39bbdb',
        textAlign: 'center'
      }}>
        患者答题 - {patientName}
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        onValuesChange={handleValuesChange}
        scrollToFirstError
        disabled={isReadOnly}
      >
        {/* 复用 MDASI-LC 组件 */}
        <MdasiLcComponent isReadOnly={isReadOnly} />

        {/* 五维健康量表EQ-5D */}
        <Card title="五维健康量表EQ-5D" style={{ marginBottom: '24px' }}>
          <div style={{ marginBottom: '16px', color: '#666' }}>
            请在下列各组选项中，指出哪一项最能反映您今天的健康状况
          </div>

          {eq5dDimensions.map((dimension) => (
            <Form.Item
              key={dimension.key}
              name={`eq5d_${dimension.key}`}
              label={dimension.label}
              rules={[{ required: true, message: '请选择选项' }]}
            >
              <Radio.Group>
                {eq5dOptions.map(option => (
                  <Radio key={option.value} value={option.value} style={{ display: 'block', marginBottom: '8px' }}>
                    {option.label}
                  </Radio>
                ))}
              </Radio.Group>
            </Form.Item>
          ))}

          <Form.Item
            name="healthScore"
            label="为了帮助您反映健康状况的好坏，请从数字0到100中填写一个最能代表您今天健康状况的数字。(0代表您想象中最差的健康状况，100代表您想象中最好的健康状况)"
            rules={[{ required: true, message: '请输入健康评分' }]}
          >
            <InputNumber
              min={0}
              max={100}
              style={{ width: '100%' }}
              placeholder="请输入0-100的数字"
            />
          </Form.Item>
        </Card>

        {/* 抑郁症状评估 */}
        <Card title="抑郁症状评估" style={{ marginBottom: '24px' }}>
          <div style={{ marginBottom: '16px', color: '#666' }}>
            在过去的两周里，你生活中以下症状出现的频率有多少
          </div>

          {depressionQuestions.map((question, index) => (
            <Form.Item
              key={`depression_${index}`}
              name={`depression_${index + 1}`}
              label={question}
              rules={[{ required: true, message: '请选择频率' }]}
            >
              <Radio.Group>
                {depressionOptions.map(option => (
                  <Radio key={option.value} value={option.value}>
                    {option.label}
                  </Radio>
                ))}
              </Radio.Group>
            </Form.Item>
          ))}

          <Form.Item
            name="depressionImpact"
            label="如果发现自己有如上症状，他们影响到你的家庭生活，工作，人际关系的程度是："
            rules={[{ required: true, message: '请选择影响程度' }]}
          >
            <Radio.Group>
              <Radio value={0}>没有困难</Radio>
              <Radio value={1}>有一些困难</Radio>
              <Radio value={2}>很多困难</Radio>
              <Radio value={3}>非常困难</Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            label="抑郁症状总分"
          >
            <InputNumber value={depressionScore} readOnly style={{ width: '100%' }} />
          </Form.Item>
        </Card>

        {/* 焦虑症状评估 */}
        <Card title="焦虑症状评估" style={{ marginBottom: '24px' }}>
          <div style={{ marginBottom: '16px', color: '#666' }}>
            根据过去两周的状况，请您回答是否存在下列描述的状况及频率，请看清楚问题后选择符合您的选项。
          </div>

          {anxietyQuestions.map((question, index) => (
            <Form.Item
              key={`anxiety_${index}`}
              name={`anxiety_${index + 1}`}
              label={question}
              rules={[{ required: true, message: '请选择频率' }]}
            >
              <Radio.Group>
                {anxietyOptions.map(option => (
                  <Radio key={option.value} value={option.value}>
                    {option.label}
                  </Radio>
                ))}
              </Radio.Group>
            </Form.Item>
          ))}

          <Form.Item
            label="焦虑症状总分"
          >
            <InputNumber value={anxietyScore} readOnly style={{ width: '100%' }} />
          </Form.Item>
        </Card>

        <Form.Item style={{ textAlign: 'center', marginTop: '32px' }}>
          {!isReadOnly && (
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              size="large"
              style={{
                backgroundColor: '#39bbdb',
                borderColor: '#39bbdb',
                minWidth: '120px'
              }}
            >
              提交问卷
            </Button>
          )}
          {isReadOnly && (
            <div style={{ 
              padding: '16px', 
              backgroundColor: '#f0f8ff', 
              borderRadius: '8px',
              color: '#666',
              textAlign: 'center'
            }}>
              此为已完成的答题记录，仅供查看
            </div>
          )}
        </Form.Item>
      </Form>
    </div>
  );
};

// 项目2问卷组件（复用MDASI-LC + 项目2特有的EQ-5D）
const Project2Questionnaire: React.FC<QuestionnaireProps> = ({ 
  patientId, 
  patientName, 
  isReadOnly = false, 
  initialValues = {}, 
  onSubmit 
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 设置初始值
  React.useEffect(() => {
    if (initialValues && Object.keys(initialValues).length > 0) {
      form.setFieldsValue(initialValues);
    }
  }, [initialValues, form]);

  // 项目2特有的EQ-5D维度和选项
  const eq5dDimensionsProject2 = [
    { 
      key: 'mobility', 
      label: '行动能力（MO）',
      options: [
        { value: 1, label: '我四处走动没有困难' },
        { value: 2, label: '我四处走动有一点困难' },
        { value: 3, label: '我四处走动有中度的困难' },
        { value: 4, label: '我四处走动有严重的困难' },
        { value: 5, label: '我无法四处走动' }
      ]
    },
    { 
      key: 'selfCare', 
      label: '自我照顾能力（SC）',
      options: [
        { value: 1, label: '我自己洗澡或穿衣没有困难' },
        { value: 2, label: '我自己洗澡或穿衣有一点困难' },
        { value: 3, label: '我自己洗澡或穿衣有中度的困难' },
        { value: 4, label: '我自己洗澡或穿衣有严重的困难' },
        { value: 5, label: '我无法自己洗澡或穿衣' }
      ]
    },
    { 
      key: 'usualActivities', 
      label: '日常活动能力（UA）',
      options: [
        { value: 1, label: '我进行日常活动没有困难' },
        { value: 2, label: '我进行日常活动有一点困难' },
        { value: 3, label: '我进行日常活动有中度的困难' },
        { value: 4, label: '我进行日常活动有严重的困难' },
        { value: 5, label: '我无法进行日常活动' }
      ]
    },
    { 
      key: 'painDiscomfort', 
      label: '疼痛/不舒服',
      options: [
        { value: 1, label: '我没有疼痛或不舒服' },
        { value: 2, label: '我有一点疼痛或不舒服' },
        { value: 3, label: '我有中度的疼痛或不舒服' },
        { value: 4, label: '我有严重的疼痛或不舒服' },
        { value: 5, label: '我有非常严重的疼痛或不舒服' }
      ]
    },
    { 
      key: 'anxietyDepression', 
      label: '焦虑/抑郁',
      options: [
        { value: 1, label: '我没有焦虑或沮丧' },
        { value: 2, label: '我有一点焦虑或沮丧' },
        { value: 3, label: '我有中度的焦虑或沮丧' },
        { value: 4, label: '我有严重的焦虑或沮丧' },
        { value: 5, label: '我有非常严重的焦虑或沮丧' }
      ]
    }
  ];

  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      console.log('项目2问卷提交数据:', values);
      
      // 这里应该调用API提交数据
      // await submitQuestionnaire({ patientId, projectType: '2', ...values });
      
      message.success('问卷提交成功！');
      if (onSubmit) {
        onSubmit(values);
      }
    } catch (error) {
      console.error('提交失败:', error);
      message.error('提交失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '20px' }}>
      <div style={{ 
        marginBottom: '24px', 
        fontSize: '20px', 
        fontWeight: 'bold',
        color: '#39bbdb',
        textAlign: 'center'
      }}>
        患者答题 - {patientName}
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        scrollToFirstError
        disabled={isReadOnly}
      >
        {/* 复用 MDASI-LC 组件 */}
        <MdasiLcComponent isReadOnly={isReadOnly} />

        {/* 项目2特有的五维健康量表EQ-5D */}
        <Card title="五维健康量表EQ-5D" style={{ marginBottom: '24px' }}>
          <div style={{ marginBottom: '16px', color: '#666' }}>
            请在下列各组选项中，指出哪一项最能反映您今天的健康状况
          </div>

          {eq5dDimensionsProject2.map((dimension) => (
            <Form.Item
              key={dimension.key}
              name={`eq5d_${dimension.key}`}
              label={`* ${dimension.label}`}
              rules={[{ required: true, message: '请选择选项' }]}
              style={{ marginBottom: '24px' }}
            >
              <Radio.Group>
                {dimension.options.map(option => (
                  <Radio key={option.value} value={option.value} style={{ display: 'block', marginBottom: '8px' }}>
                    {option.label}
                  </Radio>
                ))}
              </Radio.Group>
            </Form.Item>
          ))}

          <Form.Item
            name="healthScore"
            label="* 为了帮助您反映健康状况的好坏，请从数字0到100中填写一个最能代表您今天健康状况的数字。(说明：0代表您想象中最差的健康状况，100代表您想象中最好的健康状况)"
            rules={[{ required: true, message: '请输入健康评分' }]}
          >
            <InputNumber
              min={0}
              max={100}
              style={{ width: '100%' }}
              placeholder="请输入0-100的数字"
            />
          </Form.Item>
        </Card>

        <Form.Item style={{ textAlign: 'center', marginTop: '32px' }}>
          {!isReadOnly && (
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              size="large"
              style={{
                backgroundColor: '#39bbdb',
                borderColor: '#39bbdb',
                minWidth: '120px'
              }}
            >
              提交问卷
            </Button>
          )}
          {isReadOnly && (
            <div style={{ 
              padding: '16px', 
              backgroundColor: '#f0f8ff', 
              borderRadius: '8px',
              color: '#666',
              textAlign: 'center'
            }}>
              此为已完成的答题记录，仅供查看
            </div>
          )}
        </Form.Item>
      </Form>
    </div>
  );
};

// 主问卷组件，根据项目类型显示不同问卷
const Questionnaire: React.FC<QuestionnaireProps> = ({ 
  patientId, 
  patientName, 
  projectType = '1', 
  isReadOnly = false,
  initialValues = {},
  onSubmit,
  enrollmentNumber,
  cycleNumber,
  dispatch
}) => {
  return (
    <ConfigProvider
      theme={{
        components: {
          Radio: {
            colorPrimary: '#39bbdb',
            colorPrimaryHover: '#5ec8e3',
            colorPrimaryActive: '#2da5c7',
          },
        },
      }}
    >
      {projectType === '2' ? (
        <Project2Questionnaire 
          patientId={patientId}
          patientName={patientName}
          isReadOnly={isReadOnly}
          initialValues={initialValues}
          onSubmit={onSubmit}
        />
      ) : (
        <Project1Questionnaire 
          patientId={patientId}
          patientName={patientName}
          isReadOnly={isReadOnly}
          initialValues={initialValues}
          onSubmit={onSubmit}
          enrollmentNumber={enrollmentNumber}
          cycleNumber={cycleNumber}
          dispatch={dispatch}
        />
      )}
    </ConfigProvider>
  );
};

export default Questionnaire;
