import React from 'react';
import { Form } from 'antd';
import HistoryButton from './HistoryButton';

interface FormItemWithHistoryProps {
  fieldName: string;
  fieldLabel: string;
  patientId?: number;
  project: number;
  children: React.ReactNode;
  [key: string]: any; // 允许传递所有Form.Item的props
}

const FormItemWithHistory: React.FC<FormItemWithHistoryProps> = ({
  fieldName,
  fieldLabel,
  patientId,
  project,
  children,
  ...formItemProps
}) => {
  return (
    <div style={{ position: 'relative' }}>
      <Form.Item {...formItemProps}>
        {children}
      </Form.Item>
      {patientId && (
        <div style={{ position: 'absolute', top: '0px', right: '0px', zIndex: 10 }}>
          <HistoryButton
            fieldName={fieldName}
            fieldLabel={fieldLabel}
            patientId={patientId}
            project={project}
          />
        </div>
      )}
    </div>
  );
};

export default FormItemWithHistory; 