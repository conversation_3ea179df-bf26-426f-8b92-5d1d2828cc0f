export interface ScheduleAllItem {
  id: number;
  patientId: number;
  projectName: string;
  enrollmentNumber: number;
  cycleNumber: number;
  statusName: string;
  signStatusName: string;
  planStartDate: string;
  planEndDate: string;
  actualAnswerDate: string | null;
  answerMethodName: string;
  contactStatueName: string;
  isDelete: number;
  createTime: string;
  updateTime: string;
}

export interface ScheduleAllResponse {
  code: number;
  message: string;
  data: ScheduleAllItem[];
}
