import { request } from 'umi';
import {
  PatientAddParams,
  PatientAddResponse,
  PatientListParams,
  PatientListResponse,
  PatientUpdateParams,
  PatientUpdateResponse,
  FindPatientIdByRecordIdParams,
   FindPatientIdByRecordIdResponse,
   ShowDemographicsEmoSymCorrParams,
   ShowDemographicsEmoSymCorrResponse,
   ShowDemographicsRadSymBurParams,
   ShowDemographicsRadSymBurResponse,
   ShowDiseaseInformationEmoSymCorrParams,
   ShowDiseaseInformationEmoSymCorrResponse,
   ShowDiseaseInformationRadSymBurParams,
   ShowDiseaseInformationRadSymBurResponse,
   PreviewPDFParams,
   PreviewPDFResponse,
   UpdateDemographicsEmoSymCorrParams,
   UpdateDemographicsEmoSymCorrResponse,
   UpdateDiseaseInformationEmoSymCorrParams,
   UpdateDiseaseInformationEmoSymCorrResponse,
   UpdateDemographicsRadSymBurParams,
   UpdateDemographicsRadSymBurResponse,
   UpdateDiseaseInformationRadSymBurParams,
   UpdateDiseaseInformationRadSymBurResponse,
   ListHistoryParams,
   ListHistoryResponse,
   AddCtcaeParams,
   AddCtcaeResponse,
   ListCtcaeParams,
   ListCtcaeResponse,
   DeletePatientParams,
   DeletePatientResponse,
   ScheduleListParams,
   ScheduleListResponse,
   AnswerProjectEmoParams,
   AnswerProjectEmoResponse,
   GetAnswerProjectEmoParams,
   GetAnswerProjectEmoResponse,
   GetExpiredScheduleResponse,
 } from '@/pages/Patients/data';
import storeUtil from '@/utils/storeUtil';

// // 确保 GLPT_API 变量存在
// const API_BASE = typeof GLPT_API !== 'undefined' ? GLPT_API : '';

// 新增患者
export async function addPatient(params: PatientAddParams): Promise<PatientAddResponse> {
  console.log('API请求地址:', `${GLPT_API}/patient/add`); // 这个日志仍然可以显示完整的预期URL
  console.log('请求参数:', params);
  const tokenData = storeUtil.get('token');
  const token = tokenData && tokenData.status === storeUtil.status.SUCCESS ? tokenData.value : null;


  return request(`${GLPT_API}/patient/add`, { 
    method: 'POST',
    data: params,
    headers: { 
      Authorization: `${token}`, 
    },
  });
}

export async function updatePatient(params: PatientUpdateParams): Promise<PatientUpdateResponse> {
  console.log('API更新请求地址:', `${GLPT_API}/doctor/update`);
  console.log('更新请求参数:', params);
  // const token = localStorage.getItem('token');
  const tokenData = storeUtil.get('token');
  const token = tokenData && tokenData.status === storeUtil.status.SUCCESS ? tokenData.value : null;

  return request(`${GLPT_API}/patient/update`, { 
    method: 'PUT',
    data: params,
    headers: {
      Authorization: `${token}`,
    },
  });
}

export async function getPatientList(params: PatientListParams): Promise<PatientListResponse> {
  console.log('获取患者列表请求参数:', params);
  // const token = localStorage.getItem('token'); // 从localStorage获取token
  const tokenData = storeUtil.get('token');
  const token = tokenData && tokenData.status === storeUtil.status.SUCCESS ? tokenData.value : null;

  return request(`${GLPT_API}/patient/list`, {
    method: 'GET',
    params,
    headers: { 
      Authorization: `${token}`, 
    },
  });
}

// 上传签名文件
export async function uploadSignature(file: File, patientId: number, role: number, cycleNumber: number = 0): Promise<any> {
  console.log('上传签名文件:', patientId, role, cycleNumber);
  const tokenData = storeUtil.get('token');
  const token = tokenData && tokenData.status === storeUtil.status.SUCCESS ? tokenData.value : null;
  
  const formData = new FormData();
  formData.append('file', file);
  formData.append('patientId', patientId.toString());
  formData.append('role', role.toString()); // 0-患者，1-医生
  formData.append('cycleNumber', cycleNumber.toString());

  return request(`${GLPT_API}/file/upload`, {
    method: 'POST',
    data: formData,
    headers: {
      Authorization: `${token}`,
    },
  });
}

// 生成PDF
export async function generatePDF(patientId: number): Promise<any> {
  console.log('生成PDF:', patientId);
  const tokenData = storeUtil.get('token');
  const token = tokenData && tokenData.status === storeUtil.status.SUCCESS ? tokenData.value : null;

  return request(`${GLPT_API}/file/generatePDF`, {
    method: 'POST',
    params: { 
      patientId: patientId,
    },
    headers: {
      Authorization: `${token}`,
      'Content-Type': 'application/json',
    },
  });
}

// 根据 recordId 查找患者 ID
export async function findPatientIdByRecordId(params: FindPatientIdByRecordIdParams): Promise<FindPatientIdByRecordIdResponse> {
  // const token = localStorage.getItem('token');
  const tokenData = storeUtil.get('token');
  const token = tokenData && tokenData.status === storeUtil.status.SUCCESS ? tokenData.value : null;
  // API 文档描述请求参数格式为 queryString，且为 POST 请求
  // umi-request 对于 POST 请求，params 会被序列化到 URL 上
  return request(`${GLPT_API}/patient/findIdByPatientId`, {
    method: 'POST',
    params: {
      recordId: params.recordId,
    },
    headers: {
      Authorization: `${token}`,
      // 'Content-Type': 'application/x-www-form-urlencoded', // 如果后端确实需要 form-urlencoded，则添加此行
    },
  });
}

// 获取人口信息学信息（电子化患者）详情
export async function showDemographicsEmoSymCorr(params: ShowDemographicsEmoSymCorrParams): Promise<ShowDemographicsEmoSymCorrResponse> {
  const tokenData = storeUtil.get('token');
  const token = tokenData && tokenData.status === storeUtil.status.SUCCESS ? tokenData.value : null;
  
  return request(`${GLPT_API}/eCRF/showDemographicsEmoSymCorr`, {
    method: 'GET',
    params: {
      patientId: params.patientId,
    },
    headers: {
      Authorization: `${token}`,
    },
  });
}

// 获取人口信息学信息（放化疗患者）详情
export async function showDemographicsRadSymBur(params: ShowDemographicsRadSymBurParams): Promise<ShowDemographicsRadSymBurResponse> {
  const tokenData = storeUtil.get('token');
  const token = tokenData && tokenData.status === storeUtil.status.SUCCESS ? tokenData.value : null;
  
  return request(`${GLPT_API}/eCRF/showDemographicsRadSymBur`, {
    method: 'GET',
    params: {
      patientId: params.patientId,
    },
    headers: {
      Authorization: `${token}`,
    },
  });
}

// 获取疾病信息表单信息（电子化患者）详情
export async function showDiseaseInformationEmoSymCorr(params: ShowDiseaseInformationEmoSymCorrParams): Promise<ShowDiseaseInformationEmoSymCorrResponse> {
  const tokenData = storeUtil.get('token');
  const token = tokenData && tokenData.status === storeUtil.status.SUCCESS ? tokenData.value : null;
  
  return request(`${GLPT_API}/eCRF/showDiseaseInformationEmoSymCorr`, {
    method: 'GET',
    params: {
      patientId: params.patientId,
    },
    headers: {
      Authorization: `${token}`,
    },
  });
}

// 获取疾病信息表单信息（放化疗患者）详情
export async function showDiseaseInformationRadSymBur(params: ShowDiseaseInformationRadSymBurParams): Promise<ShowDiseaseInformationRadSymBurResponse> {
  const tokenData = storeUtil.get('token');
  const token = tokenData && tokenData.status === storeUtil.status.SUCCESS ? tokenData.value : null;
  
  return request(`${GLPT_API}/eCRF/showDiseaseInformationRadSymBur`, {
    method: 'GET',
    params: {
      patientId: params.patientId,
    },
    headers: {
      Authorization: `${token}`,
    },
  });
}

// 预览PDF
export async function previewPDF(params: PreviewPDFParams): Promise<PreviewPDFResponse> {
  console.log('预览PDF:', params.id);
  const tokenData = storeUtil.get('token');
  const token = tokenData && tokenData.status === storeUtil.status.SUCCESS ? tokenData.value : null;

  return request(`${GLPT_API}/file/preview`, {
    method: 'GET',
    params: { 
      id: params.id,
    },
    headers: {
      Authorization: `${token}`,
    },
  });
}

// 更新项目1人口信息学表单
export async function updateDemographicsEmoSymCorr(params: UpdateDemographicsEmoSymCorrParams): Promise<UpdateDemographicsEmoSymCorrResponse> {
  console.log('更新项目1人口信息学:', params);
  const tokenData = storeUtil.get('token');
  const token = tokenData && tokenData.status === storeUtil.status.SUCCESS ? tokenData.value : null;

  return request(`${GLPT_API}/eCRF/updateDemographicsEmoSymCorr`, {
    method: 'PUT',
    data: params,
    headers: {
      Authorization: `${token}`,
      'Content-Type': 'application/json',
    },
  });
}

// 更新项目1疾病信息表单
export async function updateDiseaseInformationEmoSymCorr(params: UpdateDiseaseInformationEmoSymCorrParams): Promise<UpdateDiseaseInformationEmoSymCorrResponse> {
  console.log('更新项目1疾病信息:', params);
  const tokenData = storeUtil.get('token');
  const token = tokenData && tokenData.status === storeUtil.status.SUCCESS ? tokenData.value : null;

  return request(`${GLPT_API}/eCRF/updateDiseaseInformationEmoSymCorr`, {
    method: 'PUT',
    data: params,
    headers: {
      Authorization: `${token}`,
      'Content-Type': 'application/json',
    },
  });
}

// 更新项目2人口信息学表单
export async function updateDemographicsRadSymBur(params: UpdateDemographicsRadSymBurParams): Promise<UpdateDemographicsRadSymBurResponse> {
  console.log('更新项目2人口信息学:', params);
  const tokenData = storeUtil.get('token');
  const token = tokenData && tokenData.status === storeUtil.status.SUCCESS ? tokenData.value : null;

  return request(`${GLPT_API}/eCRF/updateDemographicsRadSymBur`, {
    method: 'PUT',
    data: params,
    headers: {
      Authorization: `${token}`,
      'Content-Type': 'application/json',
    },
  });
}

// 更新项目2疾病信息表单
export async function updateDiseaseInformationRadSymBur(params: UpdateDiseaseInformationRadSymBurParams): Promise<UpdateDiseaseInformationRadSymBurResponse> {
  console.log('更新项目2疾病信息:', params);
  const tokenData = storeUtil.get('token');
  const token = tokenData && tokenData.status === storeUtil.status.SUCCESS ? tokenData.value : null;

  return request(`${GLPT_API}/eCRF/updateDiseaseInformationRadSymBur`, {
    method: 'PUT',
    data: params,
    headers: {
      Authorization: `${token}`,
      'Content-Type': 'application/json',
    },
  });
}

// 查询字段历史记录
export async function listHistory(params: ListHistoryParams): Promise<ListHistoryResponse> {
  console.log('查询历史记录:', params);
  const tokenData = storeUtil.get('token');
  const token = tokenData && tokenData.status === storeUtil.status.SUCCESS ? tokenData.value : null;

  return request(`${GLPT_API}/eCRF/listHistory`, {
    method: 'GET',
    params: {
      project: params.project,
      patientId: params.patientId,
      fieldName: params.fieldName,
    },
    headers: {
      Authorization: `${token}`,
    },
  });
}

// 新增CTCAE记录
export async function addCtcae(params: AddCtcaeParams): Promise<AddCtcaeResponse> {
  console.log('新增CTCAE请求参数:', params);
  const tokenData = storeUtil.get('token');
  const token = tokenData && tokenData.status === storeUtil.status.SUCCESS ? tokenData.value : null;

  return request(`${GLPT_API}/ctcae/addCtcae`, {
    method: 'POST',
    data: params,
    headers: {
      Authorization: `${token}`,
      'Content-Type': 'application/json',
    },
  });
}

// 查询CTCAE记录列表
export async function listCtcae(params: ListCtcaeParams): Promise<ListCtcaeResponse> {
  console.log('查询CTCAE列表请求参数:', params);
  const tokenData = storeUtil.get('token');
  const token = tokenData && tokenData.status === storeUtil.status.SUCCESS ? tokenData.value : null;

  return request(`${GLPT_API}/ctcae/list`, {
    method: 'GET',
    params,
    headers: {
      Authorization: `${token}`,
    },
  });
}

// 删除患者
export async function deletePatient(params: DeletePatientParams): Promise<DeletePatientResponse> {
  console.log('删除患者请求参数:', params);
  const tokenData = storeUtil.get('token');
  const token = tokenData && tokenData.status === storeUtil.status.SUCCESS ? tokenData.value : null;

  return request(`${GLPT_API}/patient`, {
    method: 'DELETE',
    params: {
      id: params.id,
    },
    headers: {
      Authorization: `${token}`,
    },
  });
}

// 查询答题计划列表
export async function getScheduleList(params: ScheduleListParams): Promise<ScheduleListResponse> {
  console.log('查询答题计划列表请求参数:', params);
  const tokenData = storeUtil.get('token');
  const token = tokenData && tokenData.status === storeUtil.status.SUCCESS ? tokenData.value : null;

  return request(`${GLPT_API}/schedule/list`, {
    method: 'GET',
    params: {
      pageNum: params.pageNum,
      pageSize: params.pageSize,
      patientId: params.patientId,
    },
    headers: {
      Authorization: `${token}`,
    },
  });
}

// 项目1答题
export async function answerProjectEmo(params: AnswerProjectEmoParams): Promise<AnswerProjectEmoResponse> {
  console.log('项目1答题请求参数:', params);
  const tokenData = storeUtil.get('token');
  const token = tokenData && tokenData.status === storeUtil.status.SUCCESS ? tokenData.value : null;

  return request(`${GLPT_API}/answer/answerProjectEmo`, {
    method: 'POST',
    data: params,
    headers: {
      Authorization: `${token}`,
    },
  });
}

// 查看项目1答题情况
export async function getAnswerProjectEmo(params: GetAnswerProjectEmoParams): Promise<GetAnswerProjectEmoResponse> {
  console.log('查看项目1答题情况请求参数:', params);
  const tokenData = storeUtil.get('token');
  const token = tokenData && tokenData.status === storeUtil.status.SUCCESS ? tokenData.value : null;

  return request(`${GLPT_API}/answer/getAnswerProjectEmo`, {
    method: 'GET',
    params,
    headers: {
      Authorization: `${token}`,
    },
  });
}

// 获取已过期的答题记录
export async function getExpiredSchedule(): Promise<GetExpiredScheduleResponse> {
  const tokenData = storeUtil.get('token');
  const token = tokenData && tokenData.status === storeUtil.status.SUCCESS ? tokenData.value : null;

  return request(`${GLPT_API}/schedule/getExpiredList`, {
    method: 'GET',
    headers: {
      Authorization: `${token}`,
    },
  });
}

