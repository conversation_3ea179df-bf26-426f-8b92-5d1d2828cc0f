import type { StatisticProps } from '@ant-design/pro-components';
import { ProCard, StatisticCard } from '@ant-design/pro-components';
import { connect } from 'dva';
import { Typography } from 'antd';
import "./styles.less"

interface StatisticComponentProps {
    total: number;
}

const Statistic: React.FC<StatisticComponentProps> = ({ total }) => {

    const items = [
        { key: '1', title: '入组总数', value: total, total: true },
        { key: '2', status: 'success', title: '项目完成人数', value: 0 },
        { key: '4', status: 'warning', title: '今日入组数', value: 1 },
        { key: '5', status: 'default', title: '今日答题数', value: 1 },
        { key: '6', status: 'error', title: '失访人数', value: 1 },
    ];

    return (
        <div style={{ 
            width: '100%',
            height: '100%', 
            background: 'white', 
            padding: 16, 
            borderRadius: 8,
            boxShadow: '0 1px 2px 0 rgba(0,0,0,0.03)'
          }}>
        <ProCard
        className="custom-procard-tabs"
            tabs={{
                onChange: (key) => {
                    // console.log('key', key);
                },
                items: items.map((item) => {
                    return {
                        key: item.key,
                        style: { width: '100%' },
                        label: (
                            <StatisticCard
                                statistic={{
                                    layout: 'vertical',
                                    title: item.title,
                                    value: item.value,
                                    status: item.status as StatisticProps['status'],
                                }}
                                style={{
                                    width: 140,
                                    borderInlineEnd: item.total ? '1px solid #f0f0f0' : undefined,
                                }}
                            />
                        ),
                        children: (
                            <div
                                style={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    backgroundColor: '#ffffff',
                                    height: 100,
                                    color: '#8c8c8c',
                                }}
                            >
                                {item.key === '1' ? (
                                  <>
                                    <Typography.Text style={{ marginTop: '8px' }}>
                                        详细内容请看下方
                                     </Typography.Text>
                                     <Typography.Text strong style={{ color: '#39bbdb', fontSize: '36px' }}>
                                       患者列表
                                      </Typography.Text>   
                                  </>
                                ) : item.value === 0 ? (
                                    <Typography.Title level={1} style={{ color: '#39bbdb' }}>
                                        暂无
                                    </Typography.Title>
                                ) : (
                                  `关联展示内容 ${item.title}`
                                )}
                            </div>
                        ),
                    };
                }),
            }}
        />
        </div>
    );
};

const mapStateToProps = ({ patients }: any) => ({
  total: patients.total,
});

export default connect(mapStateToProps)(Statistic);