import React, { useRef, useState, useEffect } from 'react';
import { Modal, Button, Space, message } from 'antd';
import SignatureCanvas from 'react-signature-canvas';
import PatientSignature from './Patients';

interface ElectronicSignatureProps {
  open: boolean;
  onCancel: () => void; // Overall cancellation
  onDoctorConfirm: (doctorSignatureData: string) => void; // Doctor confirms their signature
  onPatientConfirm: (patientSignatureData: string) => void; // Patient confirms their signature
  onPatientSkip: () => void; // Patient skips their signature
  patientId?: number | null;
}


const ElectronicSignature: React.FC<ElectronicSignatureProps> = (props) => {

  const {
    open,
    onCancel,
    onDoctorConfirm,
    onPatientConfirm,
    onPatientSkip,
    patientId
  } = props;

  const sigCanvas = useRef<SignatureCanvas>(null);
  const [isEmpty, setIsEmpty] = useState(true);
  const [canvasWidth, setCanvasWidth] = useState(0);
  const [canvasHeight, setCanvasHeight] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const [showPatientSignature, setShowPatientSignature] = useState(false);
  const [doctorSignatureData, setDoctorSignatureData] = useState<string>('');
  const [signatureKey, setSignatureKey] = useState(0);

  // 当模态框打开或容器大小变化时，重新计算画布尺寸
  useEffect(() => {
    if (open && containerRef.current) {
      const updateCanvasSize = () => {
        if (containerRef.current) {
          const { clientWidth, clientHeight } = containerRef.current;
          setCanvasWidth(clientWidth);
          setCanvasHeight(clientHeight);
        }
      };

      updateCanvasSize();
      window.addEventListener('resize', updateCanvasSize);

      return () => {
        window.removeEventListener('resize', updateCanvasSize);
      };
    }
  }, [open]);

  // 当模态框打开时，清除画布并更新key
  useEffect(() => {
    if (open && sigCanvas.current) {
      sigCanvas.current.clear();
      setIsEmpty(true);
      setShowPatientSignature(false);
      setDoctorSignatureData('');
      setSignatureKey(prevKey => prevKey + 1);
    }
  }, [open]);

  const handleClear = () => {
    if (sigCanvas.current) {
      sigCanvas.current.clear();
      setIsEmpty(true);
    }

  };

  const handleConfirm = () => {
    if (isEmpty) {
      message.error('请先签名');
      return;
    }

    if (sigCanvas.current) {
      const signatureData = sigCanvas.current.toDataURL('image/png');
      setDoctorSignatureData(signatureData);
      onDoctorConfirm(signatureData);
      setShowPatientSignature(true);
    }
  };

  const handleBegin = () => {
    setIsEmpty(false);
  };

  // 处理患者签名确认
  const handlePatientSignatureConfirm = (patientSignatureData: string) => {
    setShowPatientSignature(false);
    onPatientConfirm(patientSignatureData);
  };

  // 处理患者签名跳过
  const handlePatientSignatureSkip = () => {
    setShowPatientSignature(false);
    onPatientSkip();
  };

  const handlePatientSignatureCancel = () => {
    setShowPatientSignature(false);
    onCancel();
  };

  return (
    <>
      <Modal
        title="医生签名"
        open={open && !showPatientSignature}
        width={1000}
        footer={null}
        onCancel={onCancel}
        centered
        styles={{
          body: {
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            padding: '20px',
          }
        }}
      >
        <div style={{ width: '100%', textAlign: 'center', marginBottom: '20px' }}>
          <p>请在下方区域进行签名</p>
        </div>

        <div
          ref={containerRef}
          style={{
            border: '1px solid #d9d9d9',
            width: '100%',
            height: '400px',
            backgroundColor: '#f5f5f5',
            borderRadius: '4px',
            marginBottom: '20px',
            position: 'relative'
          }}
        >
          {canvasWidth > 0 && canvasHeight > 0 && (
            <SignatureCanvas
              key={`doctor-sig-${signatureKey}`}
              ref={sigCanvas}
              canvasProps={{
                width: canvasWidth,
                height: canvasHeight,
                className: 'signature-canvas',
                style: {
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%'
                }
              }}
              onBegin={handleBegin}
            />
          )}
        </div>

        <Space style={{ display: 'flex', justifyContent: 'flex-end', width: '100%' }}>
          <Button onClick={handleClear}>清除</Button>
          <Button onClick={onCancel}>取消</Button>
          <Button type="primary" onClick={handleConfirm}>
            确认
          </Button>
        </Space>
      </Modal>

      {/* 患者签名组件 */}
      <PatientSignature
        open={showPatientSignature}
        onCancel={handlePatientSignatureCancel}
        onConfirm={handlePatientSignatureConfirm}
        onSkip={handlePatientSignatureSkip}
      />
    </>
  );
};

export default ElectronicSignature;