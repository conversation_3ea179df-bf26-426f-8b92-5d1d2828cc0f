import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import type { EChartsOption } from 'echarts';

interface SeriesData {
  name: string;
  data: number[];
}

interface MultiLineChartProps {
  weekLabels: string[];
  seriesData: SeriesData[];
  title?: string;
  width?: number | string;
  height?: number | string;
}

const COLOR_PALETTE = [
  '#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE', '#3BA272', '#FC8452', '#9A60B4',
  '#EA7CCC', '#D87C7C', '#919E8B', '#D7AB82', '#6E7074', '#546570', '#C4CCD3', '#FF9F7F',
  '#FB7293', '#E062AE', '#8378EA', '#96BFFF', '#E690D1', '#E7BCF3'
];

const MultiLineChart: React.FC<MultiLineChartProps> = ({ 
  weekLabels, 
  seriesData, 
  title = '不良症状趋势图', 
  width = '100%', 
  height = '350px' // 调低高度
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    if (!chartRef.current) return;
    
    const chart = echarts.init(chartRef.current);
    
    const option: EChartsOption = {
      title: {
        text: title,
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        }
      },
      legend: {
        data: seriesData.map(item => item.name),
        top: 40, // 图例放在顶部
        itemWidth: 12,
        itemHeight: 12,
        textStyle: {
          fontSize: 12
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '18%', // 调整底部间距
        top: '25%',   // 调整顶部间距
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: weekLabels,
        axisLine: {
          lineStyle: {
            color: '#999'
          }
        },
        axisLabel: {
          color: '#666',
          rotate: 30, // 调整旋转角度
          margin: 10  // 增加标签边距
        }
      },
      yAxis: {
        type: 'value',
        min: 1,
        max: 10,
        interval: 1,
        axisLine: {
          lineStyle: {
            color: '#999'
          }
        },
        axisLabel: {
          color: '#666',
          formatter: '{value}'
        },
        splitLine: {
          lineStyle: {
            color: '#eee',
            type: 'dashed'
          }
        }
      },
      dataZoom: [{
        type: 'slider',
        show: true,
        xAxisIndex: [0],
        bottom: 10, // 调整缩略轴位置
        height: 20,
        handleSize: '80%',
        handleStyle: {
          color: '#1890ff'
        },
        fillerColor: 'rgba(24, 144, 255, 0.2)',
        borderColor: '#d9d9d9',
        filterMode: 'filter'
      }],
      series: seriesData.map((series, index) => ({
        name: series.name,
        type: 'line',
        data: series.data,
        smooth: false,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: COLOR_PALETTE[index % COLOR_PALETTE.length]
        },
        lineStyle: {
          width: 2,
          type: 'solid'
        },
        emphasis: {
          itemStyle: {
            borderWidth: 2,
            borderColor: '#333'
          }
        }
      })),
      animationDuration: 1000
    };
    
    chart.setOption(option);
    
    const handleResize = () => chart.resize();
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      chart.dispose();
    };
  }, [weekLabels, seriesData, title]);
  
  return <div ref={chartRef} style={{ width, height }} />;
};

export default MultiLineChart;