import React, { useState, useEffect } from 'react';
import { Table, Button, Tag, message, Spin, Modal } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import Questionnaire from './Question';

interface AnswerRecord {
  id: string;
  frequencyName: string; // 频次名称
  answerStatus: string; // 答题状况
  answerSignature: string; // 答题签名
  plannedStartTime: string; // 计划开始时间
  plannedEndTime: string; // 计划最后时间
  answerTime: string; // 答题时间
  answerDuration: string; // 答题用时
  answerNature: string; // 答题性质
  isLostFollowUp: boolean; // 是否失访
  paperPhoneFollowUp: string; // 纸质/电话随访
  enrollmentNumber: number;
  cycleNumber: number;
}

interface AnswerRecordProps {
  patientId: string;
  patientName: string;
  dispatch?: any;
  projectType?: string; // 添加项目类型参数
}

const AnswerRecord: React.FC<AnswerRecordProps> = ({ patientId, patientName, dispatch, projectType = '1' }) => {
  const [loading, setLoading] = useState(false);
  const [answerRecords, setAnswerRecords] = useState<AnswerRecord[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 获取答题记录数据
  useEffect(() => {
    fetchAnswerRecords();
  }, [patientId, pagination.current, pagination.pageSize]);

  const fetchAnswerRecords = async () => {
    if (!patientId || !dispatch) {
      console.warn('缺少必要参数: patientId 或 dispatch');
      return;
    }

    setLoading(true);
    try {
      // 调用答题计划接口
      const result = await dispatch({
        type: 'patients/fetchScheduleList',
        payload: {
          pageNum: pagination.current,
          pageSize: pagination.pageSize,
          patientId: parseInt(patientId, 10),
        },
      });

      if (result && result.success) {
        // 将API返回的数据转换为组件需要的格式
        const transformedData = result.data.items.map((item: any) => {
          // 添加调试信息
          console.log('API返回的完整数据:', item);
          
                     // 根据项目类型和cycleId生成频次名称
           const generateFrequencyName = () => {
             if (projectType === '1') {
               // 项目1：cycleId为0时对应基线日期，往后是以每四周为单位
               const cycleId = item.cycleId ?? item.cycleNumber ?? 0;
               if (cycleId === 0) {
                 return '基线日期';
               } else {
                 // 每四周为单位，cycleId就是随访次数
                 const weekNumber = cycleId * 4;
                 const visitNumber = cycleId; // cycleId本身就是随访次数
                 return `第${weekNumber}周的第${visitNumber}次随访`;
               }
             } else {
               // 项目2或其他项目使用原来的逻辑
               return `周期${item.cycleNumber || item.cycleId || 0}`;
             }
           };
          
          return {
            id: item.id.toString(),
            frequencyName: generateFrequencyName(),
            answerStatus: item.answerStatusName, // 直接使用API返回的状态名称
            answerSignature: item.signStatusName, // 直接使用API返回的签名状态
            plannedStartTime: item.planStartDate, // 计划开始时间
            plannedEndTime: item.planEndDate, // 计划结束时间
            answerTime: item.actualAnswerDate || '--', // 实际答题时间，为null时显示--
            answerDuration: '--', // API中没有答题用时字段，暂时显示--
            answerNature: item.answerMethodName, // 答题方式
            isLostFollowUp: item.contactStatueName !== '未失访', // 根据联系状态判断是否失访
            paperPhoneFollowUp: item.contactStatueName, // 联系状态
            enrollmentNumber: item.enrollmentNumber,
            cycleNumber: item.cycleNumber || item.cycleId || 0,
          };
        });

        setAnswerRecords(transformedData);
        setPagination(prev => ({
          ...prev,
          total: result.data.total,
        }));
      } else {
        message.error(result?.message || '获取答题记录失败');
        setAnswerRecords([]);
      }
    } catch (error) {
      console.error('获取答题记录失败:', error);
      message.error('获取答题记录失败');
      setAnswerRecords([]);
    } finally {
      setLoading(false);
    }
  };

  const columns: ColumnsType<AnswerRecord> = [
    {
      title: '频次名称',
      dataIndex: 'frequencyName',
      key: 'frequencyName',
      width: 150,
      align: 'center',
    },
    {
      title: '答题状况',
      dataIndex: 'answerStatus',
      key: 'answerStatus',
      width: 100,
      align: 'center',
      render: (status: string, record: AnswerRecord) => {
        // 直接匹配后端返回的状态值
        if (status === '已答题') {
          return (
            <Button
              type="link"
              style={{ 
                padding: 0,
                color: '#52c41a',
                fontWeight: 'normal'
              }}
              onClick={() => handleViewAnswerDetail(record)}
            >
              <Tag color="green">
                {status}
              </Tag>
            </Button>
          );
        } else {
          return (
            <Tag color="orange" style={{ cursor: 'default' }}>
              {status || '未答题'}
            </Tag>
          );
        }
      },
    },
    {
      title: '答题签名',
      dataIndex: 'answerSignature',
      key: 'answerSignature',
      width: 100,
      align: 'center',
      render: (signature: string) => (
        <Tag color={signature === '未签名' ? 'red' : 'blue'}>
          {signature}
        </Tag>
      ),
    },
    {
      title: '计划开始时间',
      dataIndex: 'plannedStartTime',
      key: 'plannedStartTime',
      width: 150,
      align: 'center',
      render: (time: string) => (
        <span>{time !== '--' ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '--'}</span>
      ),
    },
    {
      title: '计划最后时间',
      dataIndex: 'plannedEndTime',
      key: 'plannedEndTime',
      width: 150,
      align: 'center',
      render: (time: string) => (
        <span>{time !== '--' ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '--'}</span>
      ),
    },
    {
      title: '答题时间',
      dataIndex: 'answerTime',
      key: 'answerTime',
      width: 150,
      align: 'center',
      render: (time: string) => (
        <span>{time !== '--' ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '--'}</span>
      ),
    },
    {
      title: '答题用时',
      dataIndex: 'answerDuration',
      key: 'answerDuration',
      width: 100,
      align: 'center',
    },
    {
      title: '答题性质',
      dataIndex: 'answerNature',
      key: 'answerNature',
      width: 100,
      align: 'center',
      render: (nature: string) => (
        <Tag color={nature === '电子' ? 'blue' : 'default'}>
          {nature}
        </Tag>
      ),
    },
    {
      title: '是否失访',
      dataIndex: 'isLostFollowUp',
      key: 'isLostFollowUp',
      width: 100,
      align: 'center',
      render: (isLost: boolean) => (
        <Tag color={isLost ? 'red' : 'green'}>
          {isLost ? '失访' : '未失访'}
        </Tag>
      ),
    },
    {
      title: '纸质/电话随访',
      dataIndex: 'paperPhoneFollowUp',
      key: 'paperPhoneFollowUp',
      width: 120,
      align: 'center',
      render: (followUp: string, record: AnswerRecord) => {
        // 直接匹配后端返回的状态值来判断是否显示答题按钮
        const shouldShowAnswerButton = record.answerStatus !== '已答题' && !record.isLostFollowUp;
        
        console.log('答题按钮显示判断:', {
          answerStatus: record.answerStatus,
          shouldShow: shouldShowAnswerButton,
          isLostFollowUp: record.isLostFollowUp,
          followUp: followUp
        });
        
        return (
          <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '8px' }}>
            {/* 只在失访时显示状态文字，未失访时不显示 */}
            {record.isLostFollowUp && (
              <span style={{ fontSize: '12px', color: '#f5222d' }}>失访</span>
            )}
            {shouldShowAnswerButton && (
              <Button
                type="primary"
                size="small"
                style={{
                  backgroundColor: '#39bbdb',
                  borderColor: '#39bbdb',
                  fontSize: '12px',
                  height: '24px',
                  padding: '0 8px'
                }}
                onClick={() => handleStartAnswer(record)}
              >
                答题
              </Button>
            )}
          </div>
        );
      },
    },
  ];

  // 处理查看已答题详情（只读模式）
  const handleViewAnswerDetail = async (record: AnswerRecord) => {
    if (!dispatch) {
      message.error("功能异常，请联系管理员");
      return;
    }

    let initialValues = {};

    // 只处理项目1的已答题详情获取
    if (projectType === '1' && record.answerStatus === '已答题') {
      const key = 'loadingDetails';
      message.loading({ content: '正在加载答题详情...', key, duration: 0 });

      const result = await dispatch({
        type: 'patients/fetchAnswerProjectEmo',
        payload: {
          patientId: parseInt(patientId, 10),
          enrollmentNumber: record.enrollmentNumber,
          cycleNumber: record.cycleNumber,
        },
      });
      message.destroy(key);

      if (result && result.success) {
        const data = result.data;
        const formValues: any = {
          otherSymptoms: data.q23,
          eq5d_mobility: data.q24,
          eq5d_selfCare: data.q25,
          eq5d_usualActivities: data.q26,
          eq5d_painDiscomfort: data.q27,
          eq5d_anxietyDepression: data.q28,
          healthScore: data.q29,
          depressionImpact: data.q39,
        };
        for (let i = 1; i <= 16; i++) { formValues[`symptom_${i}`] = data[`q${i}`]; }
        for (let i = 1; i <= 6; i++) { formValues[`interference_${i + 16}`] = data[`q${i+16}`]; }
        for (let i = 1; i <= 9; i++) { formValues[`depression_${i}`] = data[`q${i + 29}`]; }
        for (let i = 1; i <= 7; i++) { formValues[`anxiety_${i}`] = data[`q${i + 40}`]; }
        
        initialValues = formValues;
      } else {
        return; // 获取失败则不显示弹窗
      }
    } else if (projectType !== '1' && record.answerStatus === '已答题') {
        message.info('该项目暂不支持在线查看详情');
        return;
    }

    Modal.info({
      title: <span style={{ color: '#39bbdb' }}>答题详情 - {record.frequencyName}</span>,
      width: 1400,
      icon: null,
      style: { top: 20, maxHeight: '95vh' },
      bodyStyle: { 
        maxHeight: 'calc(95vh - 150px)', 
        overflowY: 'auto',
        padding: '16px 24px 16px 24px',
        marginRight: '8px' // 为滚动条预留空间
      },
      okText: '关闭',
      okButtonProps: {
        style: {
          background: '#39bbdb',
          borderColor: '#39bbdb',
        }
      },
      content: (
        <div style={{ padding: '0' }}>
          <Questionnaire 
            patientId={patientId}
            patientName={patientName}
            projectType={projectType}
            isReadOnly={true}
            initialValues={initialValues} // 这里需要获取已答题的数据
            onSubmit={() => {}} // 只读模式不需要提交
          />
        </div>
      ),
      closable: true,
      closeIcon: <span style={{ fontSize: '18px', fontWeight: 'bold', marginRight: '8px' }}>×</span>,
    });
  };

  // 处理开始答题
  const handleStartAnswer = (record: AnswerRecord) => {
    Modal.info({
      title: <span style={{ color: '#39bbdb' }}>患者答题 - {record.frequencyName}</span>,
      width: 1400,
      icon: null,
      style: { top: 20, maxHeight: '95vh' },
      bodyStyle: { 
        maxHeight: 'calc(95vh - 150px)', 
        overflowY: 'auto',
        padding: '16px 24px 16px 24px',
        marginRight: '8px' // 为滚动条预留空间
      },
      footer: null, // 移除默认footer，使用问卷内部的提交按钮
      content: (
        <div style={{ padding: '0' }}>
          <Questionnaire 
            patientId={patientId}
            patientName={patientName}
            projectType={projectType}
            isReadOnly={false}
            enrollmentNumber={record.enrollmentNumber}
            cycleNumber={record.cycleNumber}
            dispatch={dispatch}
            onSubmit={(values) => {
              console.log('问卷提交:', values);
              Modal.destroyAll();
              // 提交成功后刷新答题记录
              fetchAnswerRecords();
            }}
          />
        </div>
      ),
      closable: true,
      closeIcon: <span style={{ fontSize: '18px', fontWeight: 'bold', marginRight: '8px' }}>×</span>,
    });
  };

  // 获取答题详情数据（模拟已答题的数据）
  const getAnswerDetailData = (record: AnswerRecord) => {
    // 这里应该从API获取真实的已答题数据
    // 现在返回模拟数据
    if (record.answerStatus === '已答题') {
      return {
        // 模拟一些已填写的数据
        symptom_1: 3,
        symptom_2: 2,
        symptom_3: 1,
        // ... 更多已答题数据
      };
    }
    return {};
  };

  // 处理分页变化
  const handleTableChange = (paginationConfig: any) => {
    setPagination(prev => ({
      ...prev,
      current: paginationConfig.current,
      pageSize: paginationConfig.pageSize,
    }));
  };

  return (
    <div style={{ padding: '16px 0' }}>
      <Table
        columns={columns}
        dataSource={answerRecords}
        rowKey="id"
        loading={loading}
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
        }}
        onChange={handleTableChange}
        size="middle"
        bordered
        scroll={{ x: 1300 }}
        style={{
          backgroundColor: '#fff',
        }}
      />
      
      {answerRecords.length === 0 && !loading && (
        <div style={{ 
          textAlign: 'center', 
          padding: '40px 0', 
          color: '#999' 
        }}>
          暂无答题记录
        </div>
      )}
    </div>
  );
};

export default AnswerRecord;
