
export interface AdverseEventGrade {
    grade1: string | null;
    grade2: string | null;
    grade3: string | null;
    grade4: string | null;
    grade5: string | null;
    description?: string;
  }
  
  export interface AdverseEvent {
    socId: string;
    eventName: string;
    eventNameEn?: string;
    grades: AdverseEventGrade;
    meddraCode?: string;
  }
  
  export interface SOCData {
    socId: string;
    socName: string;
    socNameEn?: string;
    adverseEvents: AdverseEvent[];
  }