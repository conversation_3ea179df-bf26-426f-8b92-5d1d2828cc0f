import React, { useRef, useState, useEffect } from 'react';
import { Modal, Button, Space, message } from 'antd';
import SignatureCanvas from 'react-signature-canvas';

interface PatientSignatureProps {
    open: boolean;
    onCancel: () => void;
    onConfirm: (signatureData: string) => void;
    onSkip: () => void; // 新增跳过功能
}

const PatientSignature: React.FC<PatientSignatureProps> = (props) => {
    const {
        open,
        onCancel,
        onConfirm,
        onSkip,// 默认显示全部评分
    } = props;

    const sigCanvas = useRef<SignatureCanvas>(null);
    const [isEmpty, setIsEmpty] = useState(true);
    const [canvasWidth, setCanvasWidth] = useState(0);
    const [canvasHeight, setCanvasHeight] = useState(0);
    const containerRef = useRef<HTMLDivElement>(null);
    const [signatureKey, setSignatureKey] = useState(0);

    // 当模态框打开或容器大小变化时，重新计算画布尺寸
    useEffect(() => {
        if (open && containerRef.current) {
            const updateCanvasSize = () => {
                if (containerRef.current) {
                    const { clientWidth, clientHeight } = containerRef.current;
                    setCanvasWidth(clientWidth);
                    setCanvasHeight(clientHeight);
                }
            };

            updateCanvasSize();
            window.addEventListener('resize', updateCanvasSize);

            return () => {
                window.removeEventListener('resize', updateCanvasSize);
            };
        }
    }, [open]);

    // 当模态框打开时，清除画布并更新key
    useEffect(() => {
        if (open && sigCanvas.current) {
            sigCanvas.current.clear();
            setIsEmpty(true);
            setSignatureKey(prevKey => prevKey + 1);
        }
    }, [open]);

    const handleClear = () => {
        if (sigCanvas.current) {
            sigCanvas.current.clear();
            setIsEmpty(true);
        }
    };

    const handleConfirm = () => {
        if (isEmpty) {
            message.error('请先签名');
            return;
        }

        if (sigCanvas.current) {
            const signatureData = sigCanvas.current.toDataURL('image/png');
            onConfirm(signatureData);
        }
    };

    const handleBegin = () => {
        setIsEmpty(false);
    };

    const handleSkip = () => {
        // 确认是否跳过
        Modal.confirm({
            title: '确认跳过',
            content: '您确定要跳过患者签名吗？',
            onOk: () => {
                onSkip();
            },
            okText: '确认',
            cancelText: '取消',
            icon: null,
            centered: true,
            maskClosable: false,
            okButtonProps: {
                style: {
                    backgroundColor: '#39bbdb',
                    borderColor: '#39bbdb'
                }
            },
            cancelButtonProps: {
                style: {
                    borderColor: '#d9d9d9'
                }
            }
        });
    };

    return (
        <Modal
            title="患者签名"
            open={open}
            width={1000}
            footer={null}
            onCancel={onCancel}
            centered
            styles={{
                body: {
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    padding: '20px',
                }
            }}
        >
            <div style={{ width: '100%', textAlign: 'center', marginBottom: '20px' }}>
                <p>请在下方区域进行签名</p>
            </div>

            <div
                ref={containerRef}
                style={{
                    border: '1px solid #d9d9d9',
                    width: '100%',
                    height: '400px',
                    backgroundColor: '#f5f5f5',
                    borderRadius: '4px',
                    marginBottom: '20px',
                    position: 'relative'
                }}
            >
                {canvasWidth > 0 && canvasHeight > 0 && (
                    <SignatureCanvas
                        key={`patient-sig-${signatureKey}`}
                        ref={sigCanvas}
                        canvasProps={{
                            width: canvasWidth,
                            height: canvasHeight,
                            className: 'signature-canvas',
                            style: {
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                width: '100%',
                                height: '100%'
                            }
                        }}
                        onBegin={handleBegin}
                    />
                )}
            </div>

            <Space style={{ display: 'flex', justifyContent: 'flex-end', width: '100%' }}>
                <Button onClick={handleClear}>清除</Button>
                <Button onClick={onCancel}>取消</Button>
                <Button onClick={handleSkip} style={{ marginRight: '8px' }}>跳过</Button>
                <Button type="primary" onClick={handleConfirm}>
                    确认
                </Button>
            </Space>
        </Modal>
    );
};

export default PatientSignature;