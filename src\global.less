

@media (max-width: 575px) {
    .invisible-sm {
        display: none !important;
    }
}

.custom_toolbar_buttom {
    padding: 4px 10px;
    color: rgb(255 255 255);
    font-weight: 500;
    background-color: #39bbdb;
    border-radius: 6px;
}

.custom-input {
    .ant-input {
      width: 240px; // 设置输入框宽度
      border-color: #d9d9d9; // 默认边框颜色
  
      &:focus,
      &.ant-input-focused {
        border-color: #39bbdb !important; 
        box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2) !important; 
      }
    }
  }

.custom_toolbar_buttom_danger {
    padding: 4px 10px;
    color: rgb(255 255 255);
    font-weight: 500;
    background-color: #f83a3a;
    border-radius: 6px;
}

.custom-table {
    .ant-table-thead > tr > th {
      background-color: #39bbdb30; 
    }
  
    .ant-pro-table-search {
        .ant-input-affix-wrapper:focus,
        .ant-input-affix-wrapper-focused {
          border-color: #39bbdb !important;
          box-shadow: 0 0 0 2px rgba(57, 187, 219, 0.2) !important;
        }
        
        .ant-select-selector:focus,
        .ant-select-focused .ant-select-selector {
          border-color: #39bbdb !important;
          box-shadow: 0 0 0 2px rgba(57, 187, 219, 0.2) !important;
        }
    }

    .ant-pro-table-column-setting-list {
        .ant-checkbox-checked .ant-checkbox-inner {
          background-color: #39bbdb !important;
          border-color: #39bbdb !important;
        }
      
        .ant-checkbox-indeterminate .ant-checkbox-inner {
          background-color: #39bbdb !important;
          border-color: #39bbdb !important;
        }
      
        .ant-checkbox-wrapper:hover .ant-checkbox-inner,
        .ant-checkbox:hover .ant-checkbox-inner {
          border-color: #39bbdb !important;
        }
      }

}

.ant-table-cell-fix-right {
  background: #fcfcfc !important; // 背景设为白色，避免透明
  box-shadow: -5px 0 5px -5px rgba(0, 0, 0, 0.1); // 可选：添加阴影增强视觉分层
}







