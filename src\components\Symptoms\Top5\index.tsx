import React from 'react';
import { Table } from 'antd';
import type { TableProps } from 'antd';
import styles from './styles.less';
import './styles.less';


interface DataType {
  key: string;
  name: string;
  max: number;
  min: number;
}

const columns: TableProps<DataType>['columns'] = [
  {
    title: '症状',
    dataIndex: 'name',
    align: 'center',
  },
  {
    title: '最大值',
    dataIndex: 'max',
    align: 'center',
  },
  {
    title: '最小值',
    dataIndex: 'min',
    align: 'center',
  },
];

const data: DataType[] = [
  {
    key: '1',
    name: '恶心',
    max: 6,
    min: 1,
  },
  {
    key: '2',
    name: '麻木',
    max: 6,
    min: 1,
  },
  {
    key: '3',
    name: '头晕',
    max: 6,
    min: 1,
  },
  {
    key: '4',
    name: '乏力',
    max: 6,
    min: 1,
  },
  {
    key: '5',
    name: '失眠',
    max: 6,
    min: 1,
  },
];

const Top: React.FC = () => (
  <Table<DataType>
    columns={columns}
    dataSource={data}
    bordered
    title={() => <div className={styles.tableTitle}>TOP5症状</div>}
  />
);

export default Top;