/* .login_bg {
  background-image: url('../../assets/bg.jpg');
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  background-attachment: fixed;
  height: 100vh;
} */

.bodyContent {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -60%);
  /* background-color: white;
  border-radius: 2px; */
  /* box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05); */
}

.login_bg {
  background-color: #39bbdb4a;
  height: 100vh;
}

.switchButton {
  position: absolute;
  top: 20px;
  right: 20px;
  font-weight: bold;
}

.login_form {
  width: 350px;
}

.login_form_button {
  background-color: #39bbdb;
  width: 100%;
}

.login_img {
  width: 200px;
  margin-bottom: 10px;
}

.form_title {
  text-align: center;
  margin-bottom: 20px;
  font-size: 32px;
}

.footer {
  position: fixed;
  bottom: 0;
  height: 5rem;
  text-align: center;
  background-color: #c6ebf5;
  width: 100%;
  padding-top: 0.8rem;
}

.footer span {
  display: block;
  font-size: 1rem;
}
