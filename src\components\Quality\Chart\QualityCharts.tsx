import React from 'react';
import MultiLineChart from './MultiLineChart';

const QualityCharts: React.FC = () => {
  const weekLabels = [
    '1', '2', '3', '4', '5', 
    '6', '7', '8', '9', '10',
    '11', '12', '13', '14', '15'
  ];

  const symptomData = [
    { name: '入组研究对象数', data: [3, 4, 5, 6, 7, 6, 5, 4, 3, 2, 3, 4, 5, 4, 3] },
    { name: '研究对象答题数', data: [2, 3, 4, 5, 6, 7, 6, 5, 4, 3, 2, 3, 4, 5, 4] },
    { name: '完成研究对象数', data: [4, 5, 4, 3, 2, 3, 4, 5, 6, 7, 6, 5, 4, 3, 2] },
    { name: '脱组研究对象数', data: [5, 6, 7, 6, 5, 4, 3, 2, 3, 4, 5, 6, 7, 6, 5] },
  ];

  return (
    <div style={{ 
      padding: '20px', 
      width: '100%',
      maxWidth: '1800px', 
      margin: '0 auto',
      boxSizing: 'border-box'
    }}>
      <MultiLineChart 
        weekLabels={weekLabels} 
        seriesData={symptomData}
        height="450px"
      />
    </div>
  );
};

export default QualityCharts;