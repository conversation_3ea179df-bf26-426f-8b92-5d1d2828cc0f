import { PlusOutlined } from '@ant-design/icons';
import type { ActionType } from '@ant-design/pro-components';
import { ProTable, ProSchemaValueEnumObj } from '@ant-design/pro-components';
import { Button, message } from 'antd';
import { useRef, useState } from 'react';
import { connect } from 'dva';
import { getColumns } from './ListName';
import ConsentForm from '../ConsentForm';
import { PatientListParams } from '@/pages/Patients/data';
import './styles.less';

interface PatientListProps {
  dispatch: any;
  loading: boolean;
  patientList: any[];
  total: number;
}

const PatientsList: React.FC<PatientListProps> = (props) => {
  const { dispatch, loading, patientList, total } = props;
  const actionRef = useRef<ActionType>();
  const [consentVisible, setConsentVisible] = useState(false);
  const [currentProject, setCurrentProject] = useState<string | number>('1'); // 初始项目
  const [columns, setColumns] = useState(() => getColumns('1', dispatch)); // 传递dispatch参数
  const [queryLoading, setQueryLoading] = useState(false); // 添加查询加载状态
  
  // 根据项目类型获取列状态配置
  const getColumnsState = (projectType: string | number) => {
    const baseState = {
      sex: { show: false },
      age: { show: false },
      department: { show: false },
      doctor: { show: false },
      nurse: { show: false },
      follow: { show: false },
      enrollmentStatus: { show: false },
    };

    if (String(projectType) === '2') {
      // 项目2时，CTCAE相关列默认显示
      return {
        ...baseState,
        ctcaeAdd: { show: true },
        ctcaeRecord: { show: true },
      };
    } else {
      // 项目1时，CTCAE相关列默认隐藏
      return {
        ...baseState,
        ctcaeAdd: { show: false },
        ctcaeRecord: { show: false },
      };
    }
  };

  const [columnsState, setColumnsState] = useState(() => getColumnsState('1'));

  const handleCreate = () => {
    setConsentVisible(true);
  };

  const handleConsentConfirm = (formValues: any) => {
    try {
      console.log('接收到的表单值:', formValues);
      
      // 创建成功后刷新列表
      actionRef.current?.reload();
    } catch (error) {
      console.error('创建患者错误:', error);
      message.error('创建研究对象失败');
    }
  };

  // 刷新列表的方法
  const refreshList = () => {
    actionRef.current?.reload();
  };

  const fetchData = async (params: any) => {
    try {
      // 设置查询加载状态
      setQueryLoading(true);
      
      // 转换参数格式
      const { current, pageSize, projectName, ...rest } = params; // projectName will be '1', '2', etc. from form, or undefined

      let projectValue: string | number = '1'; // Default project
      if (projectName !== undefined && projectName !== null && projectName !== '') {
        projectValue = projectName;
      }

      // 传递dispatch和刷新方法给getColumns
      setColumns(getColumns(String(projectValue), dispatch, refreshList)); 

      const queryParams: PatientListParams = {
        pageNum: current,
        pageSize,
        project: parseInt(String(projectValue), 10), // Ensure project is a number for the API
        ...rest,
      };

      // 调用模型获取数据，result 将是 effect 返回的对象
      const result = await dispatch({
        type: 'patients/fetchPatientList',
        payload: queryParams,
      });

      if (result && result.success) {
        // 打印获取到的数据，便于调试
        console.log('获取到的患者列表数据 (from effect):', result.data);
        
        // 直接使用从 effect 返回的数据
        return {
          data: result.data,
          success: true,
          total: result.total,
        };
      }
      
      // 如果 effect 返回失败或 dispatch 出现问题
      return {
        data: [],
        success: false,
        total: 0,
      };
    } catch (error) {
      console.error('查询数据错误:', error);
      return {
        data: [],
        success: false,
        total: 0,
      };
    } finally {
      // 无论成功失败都要关闭加载状态
      setQueryLoading(false);
    }
  };

  // 处理查询按钮点击
  const handleQuery = (formRef: any) => {
    setQueryLoading(true);
    formRef.submit();
  };

  // 处理重置按钮点击
  const handleReset = (formRef: any) => {
    setQueryLoading(false);
    formRef.reset();
  };

  return (
    <>
      <ProTable
        request={fetchData}
        style={{ marginTop: '24px' }}
        columns={columns}
        actionRef={actionRef}
        cardBordered
        className="custom-table"
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos-v2',
          persistenceType: 'localStorage',
          defaultValue: columnsState,
          onChange(value) {
            console.log('value: ', value);
          },
        }}
        rowKey="id"
        search={{
          labelWidth: 'auto',
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        form={{
          initialValues: { projectName: '1' }, 
          submitter: {
            render: (props, dom) => {
              return [
                <Button
                  type="default"
                  onClick={() => handleReset(props)}
                  style={{ marginRight: 8 }}
                >
                  重置
                </Button>,
                <Button
                  type="primary"
                  onClick={() => handleQuery(props)}
                  className="custom_toolbar_buttom"
                  loading={queryLoading}
                >
                  查询
                </Button>,
              ];
            },
          },
        }}
        pagination={{
          pageSize: 10,
          onChange: (page) => console.log(page),
        }}
        dateFormatter="string"
        headerTitle={
          <span style={{ color: '#39bbdb' }}>患者列表</span>
        }
        toolBarRender={() => [
          <Button
            className="custom_toolbar_buttom"
            key="button"
            icon={<PlusOutlined />}
            onClick={handleCreate}
            type="primary"
          >
            新建研究对象
          </Button>,
        ]}
        loading={loading || queryLoading}
        scroll={{ x: 800 }}
      />
      <ConsentForm
        open={consentVisible}
        onCancel={() => setConsentVisible(false)}
        onConfirm={handleConsentConfirm}
      />
    </>
  );
};

// 连接 dva 模型
const mapStateToProps = ({ patients, loading }: any) => ({
  patientList: patients.patientList,
  total: patients.total,
  loading: loading.effects['patients/fetchPatientList'],
});

export default connect(mapStateToProps)(PatientsList);