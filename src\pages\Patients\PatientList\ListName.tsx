// columns.ts
import type { ProColumns } from '@ant-design/pro-components';
import { Button, Modal, Image, message, Form, Input, ConfigProvider, Spin, Tag } from 'antd';
import trueImage from '/src/assets/true.png'
import { history, useDispatch } from 'umi';
import ESDemoinformatics from '../eCRF/Project1/ESDemoinformatics'
import ESDiseaseInfo from '../eCRF/Project1/ESDiseaseInfo';
import RCDemoinformatics from '../eCRF/Project2/RCDemoinformatics'
import RCDiseaseInfo from '../eCRF/Project2/RCDiseaseInfo';
import CTCAEForm from '../CTCAE/create'
import CTCAEHistory from '../CTCAE/history'
import ReserchSubject from '../ReserchSubject'
import EcrfLogic from '../eCRF/EcrfLogic';
import AnswerRecord from '../Answer/Answer';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import React, { useState } from 'react';
import { patientGroupMap } from '@/utils/data';
import ModificationTipButton from '@/components/tips';

dayjs.locale('zh-cn');

// 存储刷新函数的变量
let globalRefreshList: (() => void) | undefined;

// eCRF 按钮组件，单独提取出来以便使用hooks
const EcrfButton: React.FC<{ record: any }> = ({ record }) => {
  const [ecrfVisible, setEcrfVisible] = useState(false);

  return (
    <>
      <Button
        type="link"
        style={{
          color: '#39bbdb',
        }}
        onClick={() => setEcrfVisible(true)}
      >
        操作
      </Button>
      <EcrfLogic
        visible={ecrfVisible}
        record={record}
        onClose={() => setEcrfVisible(false)}
      />
    </>
  );
};

// CTCAE 按钮组件，用于在Modal中使用hooks
const CtcaeButton: React.FC<{ record: any }> = ({ record }) => {
  const dispatch = useDispatch();
  
  return (
    <Button
      type="link"
      style={{ color: '#39bbdb' }}
      onClick={() => {
        Modal.info({
          title: <span style={{ color: '#39bbdb' }}>CTCAE记录 - {record.name}</span>,
          width: 1000,
          icon: null,
          style: { maxHeight: '80vh', overflow: 'auto' },
          okText: '关闭',
          okButtonProps: {
            style: { background: '#39bbdb', borderColor: '#39bbdb' }
          },
          // 直接嵌入CTCAE表单组件
          content: (
            <div style={{ padding: '16px 0' }}>
              <CTCAEForm 
                patientId={record.id}
                dispatch={dispatch}
                onSubmitSuccess={() => {
                  Modal.destroyAll();
                  message.success('CTCAE记录已保存');
                }}
              />
            </div>
          ),
          footer: null, // 移除默认的footer，因为CTCAEForm内部有提交按钮
          closable: true,
          closeIcon: <span style={{ fontSize: '18px', fontWeight: 'bold' }}>×</span>,
        });
      }}
    >
      添加CTCAE
    </Button>
  );
};

const CtcaeHistoryButton: React.FC<{ record: any }> = ({ record }) => {
  const dispatch = useDispatch();
  
  return (
    <Button
      type="link"
      style={{
        color: '#39bbdb',
      }}
      onClick={() => {
        Modal.info({
          title: <span style={{ color: '#39bbdb' }}>CTCAE记录 - {record.name}</span>,
          width: 1400,
          icon: null,
          style: { top: 20, maxHeight: '100vh' },
          bodyStyle: { 
            maxHeight: '100vh', 
            overflowY: 'auto',
            padding: '16px 24px' 
          },
          okText: '关闭',
          okButtonProps: {
            style: {
              background: '#39bbdb',
              borderColor: '#39bbdb',
            }
          },
          content: (
            <div style={{ padding: '0' }}>
              <CTCAEHistory 
                patientId={record.id}
                patientName={record.name}
                dispatch={dispatch}
              />
            </div>
          ),
          closable: true,
          closeIcon: <span style={{ fontSize: '18px', fontWeight: 'bold' }}>×</span>,
        });
      }}
    >
      CTCAE记录
    </Button>
  );
};

// 答题记录按钮组件，用于在Modal中使用hooks
const AnswerRecordButton: React.FC<{ record: any }> = ({ record }) => {
  const dispatch = useDispatch();
  
  return (
    <Button
      type="link"
      style={{
        color: '#39bbdb',
      }}
      onClick={() => {
        Modal.info({
          title: <span style={{ color: '#39bbdb' }}>答题记录 - {record.name}</span>,
          width: 1600,
          icon: null,
          style: { top: 20, maxHeight: '100vh' },
          bodyStyle: { 
            maxHeight: 'calc(100vh - 200px)', 
            overflowY: 'auto',
            padding: '16px 24px' 
          },
          okText: '关闭',
          okButtonProps: {
            style: {
              background: '#39bbdb',
              borderColor: '#39bbdb',
            }
          },
          content: (
            <div style={{ padding: '0' }}>
              <AnswerRecord 
                patientId={record.id || record.recordId}
                patientName={record.name}
                dispatch={dispatch}
                projectType={record.projectName && record.projectName.includes('基于电子化患者报告结局的肺癌患者情绪压力及症状负担的相关性研究') ? '1' : '2'}
              />
            </div>
          ),
          closable: true,
          closeIcon: <span style={{ fontSize: '18px', fontWeight: 'bold' }}>×</span>,
        });
      }}
    >
      查看
    </Button>
  );
};

const baseColumns: ProColumns[] = [
  {
    title: 'RecordID',
    align: 'center',
    dataIndex: 'id',
    key: 'id',
    width: 100,
  },
  {
    title: '项目名称',
    align: 'center',
    dataIndex: 'projectName',
    key: 'projectName',
    width: 150,
    valueType: 'select',
    ellipsis: true,
    valueEnum: {
      '1': { text: '基于电子化患者报告结局的肺癌患者情绪压力及症状负担的相关性研究' },
      '2': { text: '同济基于ePRO肺癌根治性同步放化疗患者症状负担和症状群研究项目' },
      // 其他项目...
    },
    render: (text: any) => (
      <span title={text}>
        {text && text.length > 5 ? text.slice(0, 5) + '...' : text}
      </span>
    ),
  },
  {
    title: '研究对象ID',
    align: 'center',
    dataIndex: 'recordId',
    key: 'recordId',
    width: 150,
    ellipsis: true,
    render: (text, record) => (
      <Button
        type="link"
        style={{
          color: '#39bbdb',
        }}
        onClick={() => {
          Modal.info({
            title: <span style={{ color: '#39bbdb' }}>变更研究对象ID</span>,
            width: 800,
            okText: '关闭',
            icon: null,
            okButtonProps: {
              style: {
                background: '#39bbdb',
                borderColor: '#39bbdb',
              }
            },
            content: (
              <div>
                <ConfigProvider
                  theme={{
                    components: {
                      Input: {
                        activeBorderColor: '#39bbdb',
                        hoverBorderColor: '#39bbdb',
                        borderRadius: 4,
                        activeShadow: '0 0 0 2px rgba(24, 144, 255, 0.2)',
                      },
                    },
                  }}
                >
                  <Form.Item
                    label="研究对象ID"
                  >
                    <Input placeholder="请输入文本" />
                  </Form.Item>
                </ConfigProvider>
              </div>
            ),
          });
        }}
      >
        {text}
      </Button>
    ),
  },
  {
    title: '研究对象姓名',
    align: 'center',
    dataIndex: 'name',
    key: 'name',
    width: 150,
    ellipsis: true,
    render: (text, record) => {
      const dispatch = useDispatch();
      
      return (
        <Button
          type="link"
          style={{
            color: '#39bbdb',
          }}
          onClick={() => {
            Modal.info({
              width: 800,
              icon: null,
              title: <span style={{ color: '#39bbdb' }}>研究对象信息</span>,
              okText: '取消',
              okButtonProps: {
                style: {
                  background: '#39bbdb',
                  borderColor: '#39bbdb',
                }
              },
              content: (
                <div>
                  <ReserchSubject
                    mode="edit"
                    projectName={record.projectName}
                    initialValues={{
                      id: record.id,
                      recordId: record.recordId,
                      name: record.name,
                      phone: record.phone,
                      projectName: record.projectName,
                      projectGroup: record.projectGroup,
                      patientGroup: patientGroupMap[record.patientGroupName] ?? 1,
                      enrollmentNumber: record.enrollmentNumber,
                      radiotherapyStartDate: record.radiotherapyStartDate ? dayjs(record.radiotherapyStartDate) : null,
                      radiotherapyEndDate: record.radiotherapyEndDate ? dayjs(record.radiotherapyEndDate) : null,
                      diagnosisDate: record.diagnosisDate ? dayjs(record.diagnosisDate) : null,
                      sex: record.genderName,
                      age: record.age,
                      department: record.department,
                      bed: record.bedNumber,
                      doctor: record.doctorName,
                      remark: record.remark,
                      consentLetter: record.consentLetter,
                      contactName: record.contactName,
                      contactPhone: record.contactPhone,
                    }}
                    dispatch={dispatch}
                    onFinish={(formData) => {
                      Modal.destroyAll();
                      if (formData.deleted) {
                        message.success('患者删除成功');
                      }
                      if (globalRefreshList && typeof globalRefreshList === 'function') {
                        setTimeout(() => {
                          globalRefreshList!();
                        }, 100); 
                      }
                    }}
                  />
                </div>
              ),
            });
          }}
        >
          {text}
        </Button>
      );
    }
  },
  {
    title: '联系人',
    align: 'center',
    dataIndex: 'contact',
    width: 100,
    render: (text, record) => (
      <Button
        type="link"
        style={{
          color: '#39bbdb',
        }}
        onClick={() => {
          Modal.info({
            title: '联系人',
            content: (
              <div>
                <p>研究对象ID: {record.title}</p>
                <p>其他信息: {record.state}</p>
              </div>
            ),
          });
        }}
      >
        查看
      </Button>
    ),
  },
  {
    title: '备注',
    align: 'center',
    dataIndex: 'remark',
    width: 100,
    render: (text) => <ModificationTipButton text={text} />,
  },
  {
    title: '性别',
    align: 'center',
    dataIndex: 'genderName',
    key: 'genderName',
    width: 70,
    render: (text) => <ModificationTipButton text={text} />,
  },

  {
    title: '年龄',
    align: 'center',
    dataIndex: 'age',
    width: 70,
    render: (text) => <ModificationTipButton text={text} />,
  },
  {
    title: '科室',
    align: 'center',
    dataIndex: 'department',
    width: 100,
    render: (text) => <ModificationTipButton text={text} />,
  },
  {
    title: '床位号',
    align: 'center',
    dataIndex: 'bedNumber',
    width: 100,
    render: (text) => <ModificationTipButton text={text} />,
  },
  {
    title: '主管医生',
    align: 'center',
    dataIndex: 'doctorName',
    width: 100,
    render: (text) => <ModificationTipButton text={text} />,
  },
  {
    title: '主管护士',
    align: 'center',
    dataIndex: 'nurse',

    width: 100,
    render: (text) => <ModificationTipButton text={text} />,
  },
  {
    title: '入组时间',
    align: 'center',
    dataIndex: 'createTime',
    valueType: 'date',
    width: 100,
    sorter: true,
  },
  {
    title: '答题状况',
    align: 'center',
    dataIndex: 'status',
    width: 100,
    filters: true,
    onFilter: true,
    ellipsis: true,
    valueEnum: {
      true: {
        text: '已答题',
      },
      false: {
        text: '未答题',
      },
    },
    render: () => {
      return (
        <Image
          src={trueImage}
          width={40}
          height={40}
          preview={false}
        />
      );
    },
  },
  {
    title: '项目分组',
    align: 'center',
    dataIndex: 'projectGroup',
    width: 100,
    filters: true,
    onFilter: true,
    ellipsis: true,
    valueEnum: {
      default: {
        text: '默认访问组',
      },
      ungrouped: {
        text: '未分组',
      },
    },
  },
];

// 项目1特有的列
const project1Columns: ProColumns[] = [
  {
    title: '确诊日期',
    align: 'center',
    dataIndex: 'diagnosisDate',
    valueType: 'date',
    sorter: true,
    width: 100,
    
    render: (text, record) => {
      
      // 只在项目1中显示确诊日期
      if (record.projectName && record.projectName.includes('基于电子化患者报告结局的肺癌患者情绪压力及症状负担的相关性研究')) {
        return (
          <Button
            type="link"
            style={{
              color: '#39bbdb',
            }}
            onClick={() => {
              Modal.info({
                title: '确诊日期',
                content: (
                  <div>
                    <p>研究对象ID: {record.title}</p>
                    <p>其他信息: {record.state}</p>
                  </div>
                ),
              });
            }}
          >
            {text}
          </Button>
        );
      }
      return null; // 项目2不显示确诊日期
    },
  },
];

// 项目2特有的列
const project2Columns: ProColumns[] = [
  {
    title: '放疗开始日期',
    align: 'center',
    dataIndex: 'radiotherapyStartDate',
    valueType: 'date',
    sorter: true,
    width: 150,
    render: (text, record) => {
      // 只在项目2中显示放疗开始日期
      if (record.projectName && record.projectName.includes('同济基于ePRO肺癌根治性同步放化疗患者症状负担和症状群研究项目')) {
        return <span>{text}</span>;
      }
      return null; // 项目1不显示放疗开始日期
    },
  },
  {
    title: '放疗结束日期',
    align: 'center',
    dataIndex: 'radiotherapyEndDate',
    valueType: 'date',
    sorter: true,
    width: 150,
    render: (text, record) => {
      // 只在项目2中显示放疗结束日期
      if (record.projectName && record.projectName.includes('同济基于ePRO肺癌根治性同步放化疗患者症状负担和症状群研究项目')) {
        return <span>{text}</span>;
      }
      return null; // 项目1不显示放疗结束日期
    },
  },
  {
    title: '添加CTCAE',
    align: 'center',
    dataIndex: 'ctcaeAdd',
    width: 100,
    render: (text, record) => <CtcaeButton record={record} />,
  },
  {
    title: 'CTCAE记录',
    align: 'center',
    dataIndex: 'ctcaeRecord',
    width: 100,
    render: (text, record) => <CtcaeHistoryButton record={record} />,
  },
];

// 创建简化的eCRF列
const createSimpleEcrfColumn = (): ProColumns => ({
  title: 'eCRF',
  align: 'center' as const,
  dataIndex: 'ecrf',
  width: 100,
  render: (text: any, record: any) => <EcrfButton record={record} />,
});

const tailColumns: ProColumns[] = [
  {
    title: '答题记录',
    align: 'center',
    dataIndex: 'record',
    width: 100,
    render: (text, record) => <AnswerRecordButton record={record} />,
  },
  
  {
    title: '注册状态',
    align: 'center',
    dataIndex: 'enrollmentStatus',
    width: 100,
  },
  {
    title: '今日答题状态',
    align: 'center',
    dataIndex: 'answerStatus',
    width: 100,
    render: (_, record) => {
      const status = record.answerStatus;
      if (status === '已答题') {
        return <Tag color="success">已答题</Tag>;
      }
      if (status === '未答题') {
        return <Tag color="warning">未答题</Tag>;
      }
      return <Tag>{status || '未知'}</Tag>;
    },
  },
  {
    title: '是否出组',
    align: 'center',
    dataIndex: 'exit',
    width: 100,
    render: (text, record) => (
      <Button
        type="link"
        style={{
          color: '#39bbdb',
        }}
        onClick={() => {
          Modal.info({
            title: '是否出组',
            content: (
              <div>
                <p>研究对象ID: {record.title}</p>
                <p>其他信息: {record.state}</p>
              </div>
            ),
          });
        }}
      >
        {text}
      </Button>
    ),
  },
  {
    title: '变更组别',
    align: 'center',
    dataIndex: 'change',
    width: 100,
    render: (text, record) => (
      <Button
        type="link"
        style={{
          color: '#39bbdb',
        }}
        onClick={() => {
          Modal.info({
            title: '变更组别',
            content: (
              <div>
                <p>研究对象ID: {record.title}</p>
                <p>其他信息: {record.state}</p>
              </div>
            ),
          });
        }}
      >
        {text}
      </Button>
    ),
  },
  {
    title: '入组次数',
    align: 'center',
    dataIndex: 'enrollmentNumber',
    width: 100,
  },
  {
    title: '再次入院',
    align: 'center',
    dataIndex: 'agin',
    width: 100,
    render: (text, record) => (
      <Button
        type="link"
        style={{
          color: '#39bbdb',
        }}
        onClick={() => {
          Modal.info({
            title: '再次入院',
            content: (
              <div>
                <p>研究对象ID: {record.title}</p>
                <p>其他信息: {record.state}</p>
              </div>
            ),
          });
        }}
      >
        {text}
      </Button>
    ),
  },
  {
    title: '症状管理',
    valueType: 'option',
    fixed: 'right',
    width: 120,

    render: (_, record) => [
      <Button
        key="view"
        type="link"
        style={{
          padding: '0 8px',
          color: '#ffffff',
          backgroundColor: '#39bbdb',
          borderRadius: '4px',
          marginRight: '8px',
          zIndex: 100,
        }}
        onClick={() => {
          // 根据项目名称推断项目ID
          const projectId = record.projectName?.includes('基于电子化患者报告结局') ? 1 : 2;
          history.push(`/symptoms?patientId=${record.id}&project=${projectId}`);
        }}
      >
        查看
      </Button>
    ],
  },
];

// 根据项目类型动态生成列
const getColumns = (projectType?: string, dispatch?: any, refreshList?: () => void) => {
  // 保存刷新函数到全局变量
  globalRefreshList = refreshList;

  const tailColumnsWithEcrf = [
    createSimpleEcrfColumn(),
    ...tailColumns, // 移除了 .slice(1) 来确保所有列都被包含
  ];

  if (projectType === '1') {
    return [...baseColumns, ...project1Columns, ...tailColumnsWithEcrf];
  } else if (projectType === '2') {
    return [...baseColumns, ...project2Columns, ...tailColumnsWithEcrf];
  }
  // 默认情况下，显示所有列（或者您可以选择只显示基础列）
  return [...baseColumns, ...project1Columns, ...project2Columns, ...tailColumnsWithEcrf];
};

// 导出列函数而不是列数组
export { getColumns };
