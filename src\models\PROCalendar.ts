import { Effect, Reducer } from 'umi';
import { message } from 'antd';
import { getScheduleAll } from '@/services/PROCalendar';
import { ScheduleAllItem } from '@/pages/PROCalendar/data';

export interface PROCalendarModelState {
  scheduleList: ScheduleAllItem[];
  loading: boolean;
}

export interface PROCalendarModelType {
  namespace: 'proCalendar';
  state: PROCalendarModelState;
  effects: {
    fetchScheduleAll: Effect;
  };
  reducers: {
    saveScheduleList: Reducer<PROCalendarModelState>;
    setLoading: Reducer<PROCalendarModelState>;
  };
}

const PROCalendarModel: PROCalendarModelType = {
  namespace: 'proCalendar',

  state: {
    scheduleList: [],
    loading: false,
  },

  effects: {
    *fetchScheduleAll(_, { call, put }) {
      yield put({ type: 'setLoading', payload: true });
      try {
        const response = yield call(getScheduleAll);
        if (response && response.code === 0) {
          yield put({
            type: 'saveScheduleList',
            payload: response.data || [],
          });
        } else {
          message.error(response?.message || '获取答题计划失败');
        }
      } catch (error) {
        console.error('获取答题计划出错:', error);
        message.error('获取答题计划失败，请检查网络连接');
      } finally {
        yield put({ type: 'setLoading', payload: false });
      }
    },
  },

  reducers: {
    saveScheduleList(state, { payload }) {
      return {
        ...state,
        scheduleList: payload,
      };
    },
    setLoading(state, { payload }) {
      return {
        ...state,
        loading: payload,
      };
    },
  },
};

export default PROCalendarModel;
