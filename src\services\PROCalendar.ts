import { request } from 'umi';
import { ScheduleAllResponse } from '@/pages/PROCalendar/data';
import storeUtil from '@/utils/storeUtil';

/**
 * @description 获取全部答题计划
 * @returns {Promise<ScheduleAllResponse>}
 */
export async function getScheduleAll(): Promise<ScheduleAllResponse> {
  const tokenData = storeUtil.get('token');
  const token = tokenData && tokenData.status === storeUtil.status.SUCCESS ? tokenData.value : null;

  return request(`${GLPT_API}/schedule/listAll`, {
    method: 'GET',
    headers: {
      Authorization: `${token}`,
    },
  });
}
