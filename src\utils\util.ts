

/**
 * @description: 去掉请求中的NULL元素
 * @param data: any
 * @return data: any
 */
export function removeNull(data: any) {
  if (Object.prototype.toString.call(data) !== '[object Object]') {
    throw new Error('request data is not a object.');
  }
  return data;
}

/**
 * 将 dataURL 转换为 File 对象
 * @param dataurl dataURL 字符串
 * @param filename 文件名
 */
export function dataURLtoFile(dataurl: string, filename: string): File {
  const arr = dataurl.split(',');
  const mime = arr[0].match(/:(.*?);/)?.[1] || 'image/png';
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], filename, { type: mime });
}