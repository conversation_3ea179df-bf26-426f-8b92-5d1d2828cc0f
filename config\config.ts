import { defineConfig } from 'umi';

export default defineConfig({
  plugins: ['@umijs/plugins/dist/antd', '@umijs/plugins/dist/dva', '@umijs/plugins/dist/request'],
  antd: {},
  dva: {},
  favicons: [
    // 官网说明：此时将指向 `/favicon.png` ，确保你的项目含有 `public/favicon.png`
    '/favicon.png',
  ],
  request: {
    dataField: 'data',
  },
  routes: [
    { path: '/', component: '@/pages/Patients/index' },
    { path: '/login', component: '@/pages/Login/index' },
    { path: '/patients', component: '@/pages/Patients/index', name: '患者管理' },
    { path: '/symptoms', component: '@/pages/Symptoms/index', name: '症状列表' },
    { path: '/quality', component: '@/pages/Quality/index', name: '项目质控' },
    { path: '/calendar', component: '@/pages/PROCalendar/index', name: 'PRO日历' },
  ],
  npmClient: 'yarn',
  jsMinifier: 'terser',
});
