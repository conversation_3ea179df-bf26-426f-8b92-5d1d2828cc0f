import { LockOutlined, UserOutlined } from '@ant-design/icons';
import { Button, Checkbox, Form, Input, Layout, ConfigProvider, message } from 'antd';
import style from './style.less';
import { connect } from 'dva';
import { LoginType } from '@/models/login';
import type { Dispatch } from 'umi';
import { useState, useEffect } from 'react';
import storeUtil from '@/utils/storeUtil';

const { Content } = Layout;

interface LoginProps {
  dispatch: Dispatch;
  data?: string;
  correct: boolean;
}

const Login: React.FC<LoginProps> = (props) => {
  const { data, correct, dispatch } = props;
  const [form] = Form.useForm();
  const [submitLoading, setSubmitLoading] = useState(false);

  // 表单提交处理
  const onFinish = (values: any) => {
    console.log('onFinish called', values);
    setSubmitLoading(true);
    dispatch({
      type: 'login/login',
      payload: {
        username: values.username,
        password: values.password,
      },
    });
  };

  // 监听登录状态变化
  useEffect(() => {
    if (submitLoading) {
      setSubmitLoading(false);
    }
  }, [data, correct]);

  return (
    <div className={style.login_bg}>
      <Content className={style.bodyContent}>
        <div className={style.form_title}>
          全病程管理平台
        </div>
        <Form
          form={form}
          onFinish={onFinish}
          className={style.login_form}
        >
          <Form.Item
            name="username"
            rules={[
              {
                required: true,
                message: '请输入用户名',
              },
              {
                min: 1,
                max: 16,
                message: '用户名长度为1-16位',
              },
            ]}
          >
            <Input
              size="large"
              prefix={<UserOutlined style={{ color: 'rgba(0,0,0,.25)' }} />}
              placeholder="用户名"
            />
          </Form.Item>
          <Form.Item 
            name="password" 
            rules={[
              { 
                required: true, 
                message: '请输入密码!' 
              },
              {
                min: 1,
                max: 16,
                message: '密码长度为1-16位',
              },
            ]}
          >
            <Input
              size="large"
              prefix={<LockOutlined style={{ color: 'rgba(0,0,0,.25)' }} />}
              type="password"
              placeholder="密码"
            />
          </Form.Item>
          <Button
            size="large"
            type="primary"
            htmlType="submit"
            loading={submitLoading}
            className={style.login_form_button}
          >
            登录
          </Button>
        </Form>
      </Content>
    </div>
  );
};


const mapStateToProps = ({ login }: { login: LoginType }) => {
  return {
    data: login.data,
    correct: login.correct,
  };
};


export default connect(mapStateToProps)(Login);