import React, { useState, useEffect } from 'react';
import type { ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { Button, Tag, Tooltip, ConfigProvider, Row, Col, Select, Card } from 'antd';
import { EyeOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import locale from 'antd/locale/zh_CN';
import { ctcaeSocData } from '../data/ctcaeSocData';
import type { SOCData, AdverseEvent } from '../data/interfaces';
import type { CtcaeRecord } from '@/pages/Patients/data';

const { Option } = Select;

interface CTCAERecord {
  id: number;
  frequency: string;
  soc: string;
  adverseEvent: string;
  level: number;
  createTime: string;
  enrollmentNumber: number;
  creator: string;
  comment: string;
  occurrenceTime: string;
}

interface CTCAEHistoryProps {
  patientId?: number;
  patientName?: string;
  dispatch?: any;
  loading?: boolean;
}

interface FilterValues {
  enrollmentNumber?: number;
  frequency?: string;
  soc?: string;
  adverseEvent?: string;
}

const CTCAEHistory: React.FC<CTCAEHistoryProps> = ({ patientId, patientName, dispatch, loading = false }) => {
  const [dataSource, setDataSource] = useState<CTCAERecord[]>([]);
  const [filteredData, setFilteredData] = useState<CTCAERecord[]>([]);
  const [filters, setFilters] = useState<FilterValues>({});
  const [apiLoading, setApiLoading] = useState(false);

  // 将API数据转换为组件内部使用的数据格式
  const convertApiDataToTableData = (apiData: CtcaeRecord[]): CTCAERecord[] => {
    return apiData.map(item => ({
      id: item.id,
      frequency: `放疗第${item.cycleNumber}周`, // 根据cycleNumber生成频次显示
      soc: item.soc,
      adverseEvent: item.adverseEvent || '',
      level: item.level,
      createTime: item.createTime,
      enrollmentNumber: item.enrollmentNumber,
      creator: `医生${item.doctor}`, // 根据doctor字段生成创建人显示
      comment: item.comment,
      occurrenceTime: item.occurrenceTime,
    }));
  };

  // 获取CTCAE数据
  const fetchCtcaeData = async () => {
    if (!patientId || !dispatch) return;

    try {
      setApiLoading(true);
      const result = await dispatch({
        type: 'patients/fetchCtcaeList',
        payload: {
          patientId,
          // 可以根据需要添加其他筛选参数
        },
      });

      if (result && result.success) {
        const convertedData = convertApiDataToTableData(result.data);
        setDataSource(convertedData);
        setFilteredData(convertedData);
      }
    } catch (error) {
      console.error('获取CTCAE数据失败:', error);
    } finally {
      setApiLoading(false);
    }
  };

  // 获取所有可用的筛选选项（基于完整的CTCAE数据）
  const getAllFilterOptions = () => {
    // 从当前数据获取入组次数和频次
    const enrollmentNumbers = [...new Set(dataSource.map(item => item.enrollmentNumber))].sort();
    const frequencies = [...new Set(dataSource.map(item => item.frequency))];
    
    // 从完整的CTCAE数据获取所有SOC
    const allSocs = ctcaeSocData.map(soc => soc.socName);
    
    return {
      enrollmentNumbers,
      frequencies,
      socs: allSocs,
    };
  };

  // 根据选中的SOC获取对应的不良事件
  const getAdverseEventsBySoc = (selectedSoc?: string) => {
    if (!selectedSoc) {
      // 如果没有选择SOC，返回所有不良事件
      const allAdverseEvents: string[] = [];
      ctcaeSocData.forEach(soc => {
        soc.adverseEvents.forEach(event => {
          allAdverseEvents.push(event.eventName);
        });
      });
      return allAdverseEvents;
    }
    
    // 根据选中的SOC返回对应的不良事件
    const selectedSocData = ctcaeSocData.find(soc => soc.socName === selectedSoc);
    return selectedSocData ? selectedSocData.adverseEvents.map(event => event.eventName) : [];
  };

  // 获取当前数据的筛选选项（用于显示实际可筛选的选项）
  const getUniqueOptions = (data: CTCAERecord[]) => {
    const enrollmentNumbers = [...new Set(data.map(item => item.enrollmentNumber))].sort();
    const frequencies = [...new Set(data.map(item => item.frequency))];
    const socs = [...new Set(data.map(item => item.soc))];
    const adverseEvents = [...new Set(data.map(item => item.adverseEvent))];
    
    return { enrollmentNumbers, frequencies, socs, adverseEvents };
  };

  // 筛选数据
  const filterData = (data: CTCAERecord[], filterValues: FilterValues) => {
    return data.filter(item => {
      if (filterValues.enrollmentNumber && item.enrollmentNumber !== filterValues.enrollmentNumber) {
        return false;
      }
      if (filterValues.frequency && item.frequency !== filterValues.frequency) {
        return false;
      }
      if (filterValues.soc && item.soc !== filterValues.soc) {
        return false;
      }
      if (filterValues.adverseEvent && item.adverseEvent !== filterValues.adverseEvent) {
        return false;
      }
      return true;
    });
  };

  useEffect(() => {
    // 加载真实数据
    fetchCtcaeData();
  }, [patientId]);

  // 处理筛选变化
  const handleFilterChange = (key: keyof FilterValues, value: any) => {
    let newFilters = { ...filters, [key]: value };
    
    // 如果改变了SOC，清空不良事件选择
    if (key === 'soc') {
      newFilters = { ...newFilters, adverseEvent: undefined };
    }
    
    setFilters(newFilters);
    const filtered = filterData(dataSource, newFilters);
    setFilteredData(filtered);
  };

  // 重置筛选
  const handleResetFilters = () => {
    setFilters({});
    setFilteredData(dataSource);
  };

  // 查询按钮处理
  const handleQuery = () => {
    const filtered = filterData(dataSource, filters);
    setFilteredData(filtered);
  };

  const { enrollmentNumbers, frequencies, socs } = getAllFilterOptions();
  const adverseEvents = getAdverseEventsBySoc(filters.soc);

  // 获取等级对应的颜色和文本
  const getLevelTag = (level: number) => {
    const levelConfig = {
      1: { color: 'green', text: 'G1-轻度' },
      2: { color: 'blue', text: 'G2-中度' },
      3: { color: 'orange', text: 'G3-重度' },
      4: { color: 'red', text: 'G4-生命威胁' },
      5: { color: 'red', text: 'G5-死亡' },
    };
    
    const config = levelConfig[level as keyof typeof levelConfig] || { color: 'default', text: `G${level}` };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const columns: ProColumns<CTCAERecord>[] = [
    {
      title: '频次',
      dataIndex: 'frequency',
      key: 'frequency',
      width: 120,
      align: 'center',
    },
    {
      title: '系统器官分类(SOC)',
      dataIndex: 'soc',
      key: 'soc',
      width: 150,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '不良事件',
      dataIndex: 'adverseEvent',
      key: 'adverseEvent',
      width: 120,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '不良事件评级',
      dataIndex: 'level',
      key: 'level',
      width: 120,
      align: 'center',
      render: (_, record) => getLevelTag(record.level),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 150,
      align: 'center',
      render: (_, record) => dayjs(record.createTime).format('YYYY-MM-DD HH:mm'),
      sorter: (a, b) => dayjs(a.createTime).unix() - dayjs(b.createTime).unix(),
    },
    {
      title: '入组次数',
      dataIndex: 'enrollmentNumber',
      key: 'enrollmentNumber',
      width: 100,
      align: 'center',
    },
    {
      title: '创建人',
      dataIndex: 'creator',
      key: 'creator',
      width: 100,
      align: 'center',
    },
    {
      title: '客观数据记录',
      dataIndex: 'comment',
      key: 'comment',
      width: 200,
      align: 'center',
      ellipsis: true,
      render: (_, record) => (
        <Tooltip title={record.comment} placement="topLeft">
          <span>{record.comment}</span>
        </Tooltip>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      align: 'center',
      render: (_, record) => [
        <Button
          key="delete"
          type="link"
          icon={<DeleteOutlined />}
          style={{ color: '#ff4d4f' }}
          onClick={() => {
            // 删除逻辑
            console.log('删除记录:', record);
          }}
        >
          删除
        </Button>,
      ],
    },
  ];

  return (
    <ConfigProvider
      locale={locale}
      theme={{
        components: {
          Button: {
            colorPrimary: '#39bbdb',
            colorPrimaryHover: '#2fa5c7',
            colorPrimaryActive: '#1e8bad',
          },
          Select: {
            activeBorderColor: '#39bbdb',
            hoverBorderColor: '#39bbdb',
          },
        },
      }}
    >
      <div style={{ marginBottom: 16 }}>
        {/* 筛选器卡片 */}
        <Card 
          size="small" 
          style={{ 
            backgroundColor: '#fafafa', 
            border: '1px solid #d9d9d9',
            borderRadius: '6px'
          }}
        >
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={12} md={6} lg={4}>
              <div style={{ marginBottom: 8 }}>
                <span style={{ fontSize: '14px', color: '#666' }}>入院次数</span>
              </div>
              <Select
                placeholder="请选择入院次数"
                allowClear
                style={{ width: '100%' }}
                value={filters.enrollmentNumber}
                onChange={(value) => handleFilterChange('enrollmentNumber', value)}
              >
                {enrollmentNumbers.map(num => (
                  <Option key={num} value={num}>{num}</Option>
                ))}
              </Select>
            </Col>
            
            <Col xs={24} sm={12} md={6} lg={5}>
              <div style={{ marginBottom: 8 }}>
                <span style={{ fontSize: '14px', color: '#666' }}>频次</span>
              </div>
              <Select
                placeholder="请选择频次"
                allowClear
                style={{ width: '100%' }}
                value={filters.frequency}
                onChange={(value) => handleFilterChange('frequency', value)}
              >
                {frequencies.map(freq => (
                  <Option key={freq} value={freq}>{freq}</Option>
                ))}
              </Select>
            </Col>
            
            <Col xs={24} sm={12} md={6} lg={5}>
              <div style={{ marginBottom: 8 }}>
                <span style={{ fontSize: '14px', color: '#666' }}>系统器官分类(SOC)</span>
              </div>
              <Select
                placeholder="请选择分类"
                allowClear
                style={{ width: '100%' }}
                value={filters.soc}
                onChange={(value) => handleFilterChange('soc', value)}
              >
                {socs.map(soc => (
                  <Option key={soc} value={soc}>{soc}</Option>
                ))}
              </Select>
            </Col>
            
            <Col xs={24} sm={12} md={6} lg={5}>
              <div style={{ marginBottom: 8 }}>
                <span style={{ fontSize: '14px', color: '#666' }}>不良事件</span>
              </div>
              <Select
                placeholder="请选择不良事件"
                allowClear
                style={{ width: '100%' }}
                value={filters.adverseEvent}
                onChange={(value) => handleFilterChange('adverseEvent', value)}
              >
                {adverseEvents.map(event => (
                  <Option key={event} value={event}>{event}</Option>
                ))}
              </Select>
            </Col>
            
            <Col xs={24} sm={24} md={24} lg={5}>
              <div style={{ marginBottom: 8, visibility: 'hidden' }}>
                <span>操作</span>
              </div>
              <div style={{ display: 'flex', gap: 8 }}>
                <Button
                  type="primary"
                  style={{ 
                    background: '#39bbdb', 
                    borderColor: '#39bbdb',
                    flex: 1
                  }}
                  onClick={handleQuery}
                >
                  查询
                </Button>
                <Button
                  onClick={handleResetFilters}
                  style={{ flex: 1 }}
                >
                  重置
                </Button>
              </div>
            </Col>
          </Row>
        </Card>
      </div>

      <ProTable<CTCAERecord>
        columns={columns}
        dataSource={filteredData}
        rowKey="id"
        loading={apiLoading}
        pagination={{
          pageSize: 6,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
          size: 'small',
        }}
        search={false}
        dateFormatter="string"
        headerTitle={
          <span style={{ color: '#39bbdb', fontSize: '16px', fontWeight: 'bold' }}>
            CTCAE记录列表 {patientName && `- ${patientName}`} 
            {Object.keys(filters).some(key => filters[key as keyof FilterValues]) && (
              <span style={{ fontSize: '14px', color: '#666', marginLeft: 8 }}>
                (已筛选 {filteredData.length} 条记录)
              </span>
            )}
          </span>
        }
        toolBarRender={() => [
          <Button
            key="refresh"
            type="primary"
            style={{ background: '#39bbdb', borderColor: '#39bbdb' }}
            onClick={() => {
              fetchCtcaeData();
            }}
          >
            刷新
          </Button>,
        ]}
        options={{
          setting: {
            listsHeight: 280,
          },
        }}
        scroll={{ x: 1200, y: 300 }}
        size="small"
        cardBordered
        style={{
          backgroundColor: '#fff',
          borderRadius: '6px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        }}
      />
    </ConfigProvider>
  );
};

export default CTCAEHistory;
