import React, { useState } from 'react';
import { Button, Tooltip } from 'antd';
import { HistoryOutlined } from '@ant-design/icons';
import HistoryModal from './HistoryModal';

interface HistoryButtonProps {
  fieldName: string;
  fieldLabel: string;
  patientId: number;
  project: number; // 0-project1人口信息学表, 1-project2人口信息学表, 2-project1疾病信息表单, 3-project2疾病信息表单
}

const HistoryButton: React.FC<HistoryButtonProps> = ({
  fieldName,
  fieldLabel,
  patientId,
  project,
}) => {
  const [historyVisible, setHistoryVisible] = useState(false);

  return (
    <>
      <Tooltip title="查看历史记录">
        <Button
          type="text"
          icon={<HistoryOutlined />}
          size="small"
          style={{
            position: 'absolute',
            top: '4px',
            right: '4px',
            zIndex: 10,
            color: '#39bbdb',
          }}
          onClick={(e) => {
            e.stopPropagation();
            setHistoryVisible(true);
          }}
        />
      </Tooltip>
      
      <HistoryModal
        visible={historyVisible}
        onClose={() => setHistoryVisible(false)}
        fieldName={fieldName}
        fieldLabel={fieldLabel}
        patientId={patientId}
        project={project}
      />
    </>
  );
};

export default HistoryButton; 