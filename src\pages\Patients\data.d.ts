// 患者创建请求参数
export interface PatientAddParams {
  recordId: number;
  name: string;
  phone: string;
  project: number;
  projectGroup: string;
  patientGroup?: number;
  enrollmentNumber?: number;
  radiotherapyStartDate?: string | null;
  radiotherapyEndDate?: string | null;
  diagnosisDate?: string | null;
  gender?: number;
  age?: number;
  department?: string;
  bedNumber?: string;
  doctor?: number;
  remark?: string;
  consentLetter?: string;
  contactName?: string;
  contactPhone?: string;
  extraPhone?: string[]; // 新增：额外电话
  nurse?: string | null; // 新增：根据API文档
  isDelete?: number; // 新增：根据API文档
}

// 患者创建响应数据
export interface PatientAddResponse {
code: number;
message: string;
data: any;
}

// 项目选项类型
export interface ProjectOption {
value: string;
label: string;
}

// 患者列表请求参数
export interface PatientListParams {
  pageNum?: number;
  pageSize?: number;
  project?: number;
  projectGroup?: string;
  patientGroup?: string;
  recordId?: number;
  name?: string;
  phone?: string;
}

// 患者列表项数据结构
export interface PatientListItem {
  id: number;
  patientId: number | null;
  name: string;
  phone: string;
  projectName: string;
  projectGroup: string;
  patientGroupName: string;
  enrollmentNumber: number;
  radiotherapyStartDate: string | null;
  radiotherapyEndDate: string | null; // 放疗结束日期
  diagnosisDate?: string | null;
  genderName: string;
  age: number;
  department: string;
  bedNumber: string;
  doctorName: string;
  remark: string;
  consentLetter: string;
  contactName: string;
  contactPhone: string;
  createTime: string;
  updateTime: string;
  answerStatus?: string; // 今日答题状态
}

// 患者列表响应数据
export interface PatientListResponse {
  code: number;
  message: string;
  data: {
    total: number;
    items: PatientListItem[];
  };
}

// 更新患者的参数类型，需要包含 id
export interface PatientUpdateParams {
  id: number; // 主键 ID，必须
  recordId?: number;
  name?: string;
  phone?: string;
  project?: number;
  projectGroup?: string;
  patientGroup?: number;
  enrollmentNumber?: number;
  radiotherapyStartDate?: string | null;
  radiotherapyEndDate?: string | null;
  diagnosisDate?: string | null;
  gender?: number;
  age?: number;
  department?: string;
  bedNumber?: string;
  doctor?: number;
  remark?: string;
  consentLetter?: string;
  contactName?: string;
  contactPhone?: string;
  nurse?: string | null;    // 新增：根据API文档
  isDelete?: number;  // 新增：根据API文档 (0 - 否, 1 - 是)
}

export interface PatientUpdateResponse {
  code: number;
  message: string;
  data: any; // 根据实际 API 响应调整
}

// 上传签名文件请求参数
export interface UploadSignatureParams {
  file: File;
  patientId: number;
  role: number; // 0-患者，1-医生
  cycleNumber: number; // 0-录入患者时的签名，1及以后-患者答题时的签名
}

// 上传签名文件响应数据
export interface UploadSignatureResponse {
  code: number;
  message: string;
  data: any;
}

// 生成PDF请求参数
export interface GeneratePDFParams {
  patientId: number;
}

// 生成PDF响应数据
export interface GeneratePDFResponse {
  code: number;
  message: string;
  data: any;
}

// 根据 recordId 查找患者 ID 请求参数
export interface FindPatientIdByRecordIdParams {
  recordId: number;
}

// 根据 recordId 查找患者 ID 响应数据
export interface FindPatientIdByRecordIdResponse {
  code: number;
  message: string;
  data: number | null; // data 直接是 patientId
}

export interface ShowDemographicsEmoSymCorrParams {
  patientId: number;
}

// 人口信息学数据响应（项目1）
export interface ShowDemographicsEmoSymCorrResponse {
  code: number;
  message: string;
  data: {
    id: number;
    patientId: number;
    name: string;
    birthDate: string;
    age: number;
    gender: number;
    ethnic: number;
    otherEthnic: string;
    maritalStatus: number;
    degree: number;
    height: number;
    weight: number;
    income: number;
    career: number;
    insurance: number;
    smokingStatus: number;
    smokingTime: number;
    drink: number;
    drinkTime: number;
    hypertension: number;
    hypertensionTime: number;
    diabetes: number;
    diabetesTime: number;
    otherDisease: number;
    otherDiseaseName: string;
    surveyMethod: number;
    identity: number;
    investigatorName: string;
    investigateDate: string;
    answerStatue: number;
    isDelete: number;
    createTime: string;
    updateTime: string;
  };
}

// 项目2的人口信息学数据请求参数
export interface ShowDemographicsRadSymBurParams {
  patientId: number;
}

// 项目2的人口信息学数据响应
export interface ShowDemographicsRadSymBurResponse {
  code: number;
  message: string;
  data: {
    id: number;
    patientId: number;
    name: string;
    gender: number;
    birthDate: string;
    illnessAge: number;
    ageRange: number;
    smokeHistory: number;
    smokingTime: number;
    drink: number;
    drinkTime: number;
    ethnic: number;
    otherEthnic: string;
    maritalStatus: number;
    degree: number;
    height: number;
    weight: number;
    workStatus: number;
    career: number;
    insurance: number;
    hypertension: number;
    hypertensionTime: number;
    diabetes: number;
    diabetesTime: number;
    otherDisease: number;
    otherDiseaseName: string;
    concomitantDiseaseAmount: number;
    answerStatue: number;
    isDelete: number;
    createTime: string;
    updateTime: string;
    bmi: number | null;
  };
}

// 项目1的疾病信息表单请求参数
export interface ShowDiseaseInformationEmoSymCorrParams {
  patientId: number;
}

// 项目1的疾病信息表单响应
export interface ShowDiseaseInformationEmoSymCorrResponse {
  code: number;
  message: string;
  data: {
    id: number;
    patientId: number;
    diseaseName: string;
    diagnosisDate: string;
    metastasis: number;
    concomitantTumors: number;
    tumors: string | null;
    answerStatue: number;
    isDelete: number;
    createTime: string;
    updateTime: string;
    m: number | null;
    tnmtype: number | null;
    n: number | null;
    t: number | null;
  };
}

// 项目2的疾病信息表单请求参数
export interface ShowDiseaseInformationRadSymBurParams {
  patientId: number;
}

// 项目2的疾病信息表单响应
export interface ShowDiseaseInformationRadSymBurResponse {
  code: number;
  message: string;
  data: {
    id: number;
    patientId: number;
    pathologicTyping: string;
    geneticMutations: string;
    tumorSite: number;
    specificTumorSite: number;
    diagnosisDate: string;
    tumorStage: number;
    mergeState: number;
    mergeTumorName: string | null;
    treatment: number;
    chemotherapyCyclesPrior: string;
    immunotherapyPrior: number; // 新字段：放疗前是否免疫治疗
    immunotherapyMaintenance: number; // 新字段：放疗结束是否维持免疫治疗
    concurrentChemotherapy: number; // 新字段：放疗期间是否同步化疗
    concurrentChemotherapyRegimen: string | null; // 新字段：同步化疗方案
    concurrentChemotherapyTime: string | null; // 新字段：同步化疗时间
    targetedTherapyMaintenance: number; // 新字段：放疗结束后是否维持靶向治疗
    answerStatue: number; // 添加状态字段
    isDelete: number;
    createTime: string;
    updateTime: string;
    tnm: string | null;
  };
}

// 预览PDF请求参数
export interface PreviewPDFParams {
  id: number; // patientId
}

// 预览PDF响应数据
export interface PreviewPDFResponse {
  code: number;
  message: string;
  data: any; // PDF文件预览数据
}

// 更新项目1人口信息学表单请求参数
export interface UpdateDemographicsEmoSymCorrParams {
  patientId: number;
  name?: string;
  birthDate?: string; // 格式为 yyyy-MM-dd
  age?: number;
  gender?: number; // 0-未知，1-男，2-女
  ethnic?: number; // 0-汉族，1-土家族，2-苗族，3-彝族，4-藏族，5-羌族，6-回族，7-其他，8-未知
  otherEthnic?: string;
  maritalStatus?: number; // 0-未婚，1-已婚，2-离异，3-丧偶，4-分居，5-其他，6-未知
  degree?: number; // 0-文盲，1-小学，2-初中，3-高中，4-大专，5-本科及以上
  height?: number; // 身高（厘米）
  weight?: number; // 体重（千克）
  income?: number; // 0-<800，1-800-1500，2-1500-3000，3-3000-5000，4->=5000，5-未知
  career?: number; // 0-农民，2-工人，3-机关事业单位，4-企业，5-离退休，6-其他，7-无业，8-未知
  insurance?: number; // 0-无，1-城镇职工医保，2-城镇居民医保，3-农村居民医保，4-商业保险，5-公费医疗，6-其他，7-未知
  smokingStatus?: number; // 0-否，1-是，2-已戒烟，3-未知
  smokingTime?: number;
  drink?: number; // 0-否，1-是，2-已戒酒，3-未知
  drinkTime?: number;
  hypertension?: number; // 0-否，1-是
  hypertensionTime?: number;
  diabetes?: number; // 0-否，1-是
  diabetesTime?: number;
  otherDisease?: number; // 0-否，1-是
  otherDiseaseName?: string;
  surveyMethod?: number; // 0-面对面调查，1-电话调查，2-其他
  identity?: number; // 0-患者本人，1-家属代答，2-其他
  investigatorName?: string;
  investigateDate?: string; // 格式为 yyyy-MM-dd
  answerStatue?: number; // 0-未完成，1-待验证，2-已完成
}

// 更新项目1人口信息学表单响应数据
export interface UpdateDemographicsEmoSymCorrResponse {
  code: number;
  message: string;
  data: any;
}

// 更新项目1疾病信息表单请求参数
export interface UpdateDiseaseInformationEmoSymCorrParams {
  patientId: number;
  diseaseName?: string;
  diagnosisDate?: string; // 格式为 yyyy-MM-dd
  tnmtype?: number; // 0-pTNM 1-cTNM 2-无TNM分期
  t?: number; // 0-Tx 1-T0 2-Tis 3-T1 4-T2 5-T3 6-T4
  n?: number; // 0-Nx 1-N0 2-N1 3-N2 4-N3
  m?: number; // 0-M0 1-M1 2-unknown
  metastasis?: number; // 0-无转移 1-局部转移 2-远处转移
  concomitantTumors?: number; // 0-否 1-是 2-未知
  tumors?: string;
  answerStatue?: number; // 0-未完成 1-待验证 2-已完成
}

// 更新项目1疾病信息表单响应数据
export interface UpdateDiseaseInformationEmoSymCorrResponse {
  code: number;
  message: string;
  data: any;
}

// 更新项目2人口信息学表单请求参数
export interface UpdateDemographicsRadSymBurParams {
  patientId: number;
  name?: string;
  gender?: number; // 0-未知，1-男，2-女
  birthDate?: string; // 格式为 yyyy-MM-dd
  illnessAge?: number;
  ageRange?: number; // 0-≤65岁，1->65岁
  smokeHistory?: number; // 0-目前抽烟，1-已戒烟，2-从不抽烟
  smokingTime?: number;
  drink?: number; // 0-否，1-是，2-已戒酒，3-未知
  drinkTime?: number;
  ethnic?: number; // 0-汉族，1-土家族，2-苗族，3-彝族，4-藏族，5-羌族，6-回族，7-其他，8-未知
  otherEthnic?: string;
  maritalStatus?: number; // 0-未婚，1-已婚，2-离异，3-丧偶，4-分居，5-其他，6-未知
  degree?: number; // 0-文盲，1-小学，2-中学，3-高中，4-大专，5-本科及以上
  height?: number; // 身高（厘米）
  weight?: number; // 体重（千克）
  workStatus?: number; // 0-工作，1-退休，2-失业
  career?: number; // 0-农民，2-工人，3-机关事业单位，4-企业，5-离退休，6-其他，7-无业，8-未知
  insurance?: number; // 0-无，1-城镇职工医保，2-城镇居民医保，3-农村居民医保，4-商业保险，5-公费医疗，6-其他，7-未知
  hypertension?: number; // 0-否，1-是
  hypertensionTime?: number;
  diabetes?: number; // 0-否，1-是
  diabetesTime?: number;
  otherDisease?: number; // 0-否，1-是
  otherDiseaseName?: string;
  concomitantDiseaseAmount?: number; // 0-0个，1-1个以上
  answerStatue?: number; // 0-未完成，1-待验证，2-已完成
}

// 更新项目2人口信息学表单响应数据
export interface UpdateDemographicsRadSymBurResponse {
  code: number;
  message: string;
  data: any;
}

// 更新项目2疾病信息表单请求参数
export interface UpdateDiseaseInformationRadSymBurParams {
  patientId: number;
  pathologicTyping?: string;
  geneticMutations?: string;
  tumorSite?: number; // 1-左肺，2-右肺
  specificTumorSite?: number; // 1-上叶，2-下叶，3-中叶
  diagnosisDate?: string; // 格式为 yyyy-MM-dd
  TNM?: string; // 注意这里是字符串类型
  tumorStage?: number; // 1-I期，2-II期，3-III期
  mergeState?: number; // 0-否，1-是，2-未知
  mergeTumorName?: string;
  treatment?: number; // 0-否，1-是
  chemotherapyCyclesPrior?: string;
  immunotherapyPrior?: number; // 0-否，1-是（放疗前是否免疫治疗）
  immunotherapyMaintenance?: number; // 0-否，1-是（放疗结束是否维持免疫治疗）
  concurrentChemotherapy?: number; // 0-否，1-是（放疗期间是否同步化疗）
  concurrentChemotherapyRegimen?: string; // 同步化疗方案
  concurrentChemotherapyTime?: string; // 同步化疗时间
  targetedTherapyMaintenance?: number; // 0-否，1-是（放疗结束后是否维持靶向治疗）
  answerStatue?: number; // 0-未完成，1-待验证，2-已完成
}

// 更新项目2疾病信息表单响应数据
export interface UpdateDiseaseInformationRadSymBurResponse {
  code: number;
  message: string;
  data: any;
}

// 历史记录查询请求参数
export interface ListHistoryParams {
  project: number; // 0-project1人口信息学表, 1-project2人口信息学表, 2-project1疾病信息表单, 3-project2疾病信息表单
  patientId: number;
  fieldName: string;
}

// 历史记录项
export interface HistoryRecord {
  id: number;
  project: number;
  patientId: number;
  originId: number;
  fieldName: string;
  oldValue: string;
  newValue: string;
  operator: string;
  isDelete: number;
  createTime: string;
  updateTime: string;
}

// 历史记录查询响应数据
export interface ListHistoryResponse {
  code: number;
  message: string;
  data: HistoryRecord[];
}

// 新增CTCAE相关接口
export interface AddCtcaeParams {
  patientId: number; // 患者的主键 ID
  enrollmentNumber: number; // 当前进组次数
  cycleNumber: number; // 周期编号
  occurrenceTime: string; // 不良事件发生时间，格式为 "yyyy-MM-dd HH:mm:ss"
  soc: string; // 系统器官分类
  adverseEvent: string; // 不良事件
  level: number; // 不良事件等级
  comment?: string; // 备注信息（可选）
}

export interface AddCtcaeResponse {
  code: number;
  message: string;
  data: null;
}

// 查询CTCAE列表请求参数
export interface ListCtcaeParams {
  patientId: number; // 患者的主键 ID（必须）
  enrollmentNumber?: number; // 当前进组次数（可选）
  cycleNumber?: number; // 周期编号（可选）
  soc?: string; // 系统器官分类（可选）
  adverseEvent?: string; // 不良事件（可选）
}

// CTCAE记录数据结构
export interface CtcaeRecord {
  id: number; // 不良事件记录的主键 ID
  patientId: number; // 患者的主键 ID
  enrollmentNumber: number; // 当前进组次数
  cycleNumber: number; // 周期编号
  occurrenceTime: string; // 不良事件发生时间，格式为 "yyyy-MM-dd HH:mm:ss"
  soc: string; // 系统器官分类
  adverseEvent: string | null; // 具体的不良事件，可能为null
  level: number; // 不良事件等级
  comment: string; // 备注信息
  doctor: string; // 负责医生的标识
  isDelete: number; // 表示该记录是否被删除，0-未删除，1-已删除
  createTime: string; // 记录的创建时间，格式为 "yyyy-MM-dd HH:mm:ss"
  updateTime: string; // 记录的更新时间，格式为 "yyyy-MM-dd HH:mm:ss"
}

// 查询CTCAE列表响应数据
export interface ListCtcaeResponse {
  code: number;
  message: string;
  data: CtcaeRecord[];
}

// 删除患者请求参数
export interface DeletePatientParams {
  id: number; // 患者的主键 ID
}

// 删除患者响应数据
export interface DeletePatientResponse {
  code: number;
  message: string;
  data: null;
}

// 答题计划列表请求参数
export interface ScheduleListParams {
  pageNum: number; // 当前页码
  pageSize: number; // 每页条数
  patientId: number; // 患者的主键ID
}

// 答题计划项数据结构
export interface ScheduleListItem {
  id: number; // 答题计划的主键ID
  patientId: number; // 患者ID
  projectName: string; // 项目名称
  enrollmentNumber: number; // 当前进组次数
  cycleNumber: number; // 周期编号
  statusName: string; // 答题状态名称，如"未答题"
  signStatusName: string; // 签名状态名称，如"未签名"
  planStartDate: string; // 计划答题开始日期，格式为"yyyy-MM-dd HH:mm:ss"
  planEndDate: string; // 计划答题结束日期，格式为"yyyy-MM-dd HH:mm:ss"
  actualAnswerDate: string | null; // 实际答题日期，若未答题则为null，格式为"yyyy-MM-dd HH:mm:ss"
  answerMethodName: string; // 答题方式名称，如"电子"
  contactStatueName: string; // 联系状态名称，如"未失访"
  isDelete: number; // 是否删除：0-否，1-是
  createTime: string; // 创建时间，格式为"yyyy-MM-dd HH:mm:ss"
  updateTime: string; // 更新时间，格式为"yyyy-MM-dd HH:mm:ss"
}

// 答题计划列表响应数据
export interface ScheduleListResponse {
  code: number;
  message: string;
  data: {
    total: number; // 总记录数
    items: ScheduleListItem[]; // 数据列表
  };
}

// 项目1答题请求参数
export interface AnswerProjectEmoParams {
  patientId: number;
  enrollmentNumber: number;
  cycleNumber: number;
  q1?: number;
  q2?: number;
  q3?: number;
  q4?: number;
  q5?: number;
  q6?: number;
  q7?: number;
  q8?: number;
  q9?: number;
  q10?: number;
  q11?: number;
  q12?: number;
  q13?: number;
  q14?: number;
  q15?: number;
  q16?: number;
  q17?: number;
  q18?: number;
  q19?: number;
  q20?: number;
  q21?: number;
  q22?: number;
  q23?: string;
  q24?: number;
  q25?: number;
  q26?: number;
  q27?: number;
  q28?: number;
  q29?: number;
  q30?: number;
  q31?: number;
  q32?: number;
  q33?: number;
  q34?: number;
  q35?: number;
  q36?: number;
  q37?: number;
  q38?: number;
  q39?: number;
  q40?: number;
  q41?: number;
  q42?: number;
  q43?: number;
  q44?: number;
  q45?: number;
  q46?: number;
  q47?: number;
  q48?: number;
}

// 项目1答题响应数据
export interface AnswerProjectEmoResponse {
  code: number;
  message: string;
  data: any;
}

// 查看项目1答题情况
export interface GetAnswerProjectEmoParams {
  patientId: number;
  enrollmentNumber: number;
  cycleNumber: number;
}

export interface AnswerProjectEmoData {
  patientId: number;
  enrollmentNumber: number;
  cycleNumber: number;
  q1?: number;
  q2?: number;
  q3?: number;
  q4?: number;
  q5?: number;
  q6?: number;
  q7?: number;
  q8?: number;
  q9?: number;
  q10?: number;
  q11?: number;
  q12?: number;
  q13?: number;
  q14?: number;
  q15?: number;
  q16?: number;
  q17?: number;
  q18?: number;
  q19?: number;
  q20?: number;
  q21?: number;
  q22?: number;
  q23?: string;
  q24?: number;
  q25?: number;
  q26?: number;
  q27?: number;
  q28?: number;
  q29?: number | null;
  q30?: number;
  q31?: number;
  q32?: number;
  q33?: number;
  q34?: number;
  q35?: number;
  q36?: number;
  q37?: number;
  q38?: number;
  q39?: number;
  q40?: number | null;
  q41?: number;
  q42?: number;
  q43?: number;
  q44?: number;
  q45?: number;
  q46?: number;
  q47?: number;
  q48?: number | null;
}

export interface GetAnswerProjectEmoResponse {
  code: number;
  message: string;
  data: AnswerProjectEmoData;
}

export interface ExpiredScheduleItem {
  patientId: number;
  name: string;
  phone: string;
  projectName: string;
  cycleNumber: number;
}

export interface GetExpiredScheduleResponse {
  code: number;
  message: string;
  data: ExpiredScheduleItem[];
}

