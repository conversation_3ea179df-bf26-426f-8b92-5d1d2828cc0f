import React, { useState, useEffect } from 'react';
import { Modal, Table, Typography, Empty, Spin } from 'antd';
import { useDispatch } from 'umi';
import { HistoryRecord } from '@/pages/Patients/data';
import dayjs from 'dayjs';

const { Text } = Typography;

interface HistoryModalProps {
  visible: boolean;
  onClose: () => void;
  fieldName: string;
  fieldLabel: string;
  patientId: number;
  project: number; // 0-project1人口信息学表, 1-project2人口信息学表, 2-project1疾病信息表单, 3-project2疾病信息表单
}

const HistoryModal: React.FC<HistoryModalProps> = ({
  visible,
  onClose,
  fieldName,
  fieldLabel,
  patientId,
  project,
}) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [historyData, setHistoryData] = useState<HistoryRecord[]>([]);

  // 获取历史记录
  const fetchHistory = async () => {
    if (!visible || !patientId || !fieldName) return;
    
    setLoading(true);
    try {
      const result: any = await dispatch({
        type: 'patients/fetchFieldHistory',
        payload: {
          project,
          patientId,
          fieldName,
        },
      });
      
      if (result?.success) {
        setHistoryData(result.data || []);
      } else {
        setHistoryData([]);
      }
    } catch (error) {
      console.error('获取历史记录失败:', error);
      setHistoryData([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible) {
      fetchHistory();
    }
  }, [visible, patientId, fieldName, project]);

  // 表格列定义
  const columns = [
    {
      title: '修改时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 150,
      render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作人',
      dataIndex: 'operator',
      key: 'operator',
      width: 120,
    },
    {
      title: '原值',
      dataIndex: 'oldValue',
      key: 'oldValue',
      width: 150,
      render: (text: string) => (
        <Text type="secondary" style={{ wordBreak: 'break-all' }}>
          {text || '(空)'}
        </Text>
      ),
    },
    {
      title: '新值',
      dataIndex: 'newValue',
      key: 'newValue',
      width: 150,
      render: (text: string) => (
        <Text style={{ wordBreak: 'break-all', color: '#39bbdb', fontWeight: 'bold' }}>
          {text || '(空)'}
        </Text>
      ),
    },
  ];

  return (
    <Modal
      title={
        <span style={{ color: '#39bbdb' }}>
          {fieldLabel} - 历史记录
        </span>
      }
      open={visible}
      onCancel={onClose}
      width={800}
      footer={null}
      style={{ maxHeight: '80vh' }}
    >
      <div style={{ marginBottom: '16px' }}>
        <Text type="secondary">
          字段名称: <Text code>{fieldName}</Text>
        </Text>
      </div>
      
      {loading ? (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" tip="正在加载历史记录..." />
        </div>
      ) : historyData.length > 0 ? (
        <Table
          columns={columns}
          dataSource={historyData}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: false,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          scroll={{ y: 400 }}
          size="small"
        />
      ) : (
        <Empty
          description="暂无历史记录"
          style={{ padding: '50px' }}
        />
      )}
    </Modal>
  );
};

export default HistoryModal; 