import { EllipsisOutlined, PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable, TableDropdown } from '@ant-design/pro-components';
import { Button, ConfigProvider, message } from 'antd';
import { useRef, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import dayjs from 'dayjs';
import request from 'umi-request';

import Alter from '../../components/Quality/Alter';
import QualityCharts from '@/components/Quality/Chart/QualityCharts';




const Quality: React.FC = () => {

  return (

    <div>
      <div style={{
                    backgroundColor: '#ffffff',
                    padding: '20px',
                    width: '100%',
                    maxWidth: '1760px',
                    margin: '0 auto',
                    boxSizing: 'border-box'
                }}>
        <Alter />
      </div>
      <div style={{
                    backgroundColor: '#ffffff',
                    padding: '20px',
                    width: '100%',
                    maxWidth: '1760px',
                    margin: '20px auto 0 auto', 
                    boxSizing: 'border-box'
                }}>
        <QualityCharts />
      </div>

    </div>


  );
};

export default Quality;