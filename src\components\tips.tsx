import React from 'react';
import { Modal, Button } from 'antd';

const showModificationTip = () => {
  Modal.info({
    width: 800,
    okText: '关闭',
    icon: null,
    okButtonProps: {
      style: {
        background: '#39bbdb',
        borderColor: '#39bbdb',
      }
    },
    content: (
      <div style={{ textAlign: 'center', padding: '20px' }}>
        <p style={{ fontSize: '16px', fontWeight: 'bold', color: '#39bbdb', marginBottom: '10px' }}>温馨提示</p>
        <p style={{ fontSize: '14px', padding: '10px', borderRadius: '4px'}}>
          如果想要修改此项，请在点击患者姓名后出现的卡片中修改
        </p>
      </div>
    ),
  });
};

interface ModificationTipButtonProps {
  text: React.ReactNode;
}

const ModificationTipButton: React.FC<ModificationTipButtonProps> = ({ text }) => {
  if (!text) {
    return null; // 或者返回一个占位符，比如 '-'
  }

  return (
    <Button
      type="link"
      style={{
        color: '#39bbdb',
      }}
      onClick={showModificationTip}
    >
      {text}
    </Button>
  );
};

export default ModificationTipButton;
