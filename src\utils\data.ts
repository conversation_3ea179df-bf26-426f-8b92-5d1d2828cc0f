// 从 data.d.ts 导入类型
import { PatientAddParams, PatientAddResponse, ProjectOption } from '@/pages/Patients/data';

// 重新导出类型，这样其他文件可以从 data.ts 导入
export type { PatientAddParams, PatientAddResponse, ProjectOption };

// 性别映射
export const genderMap = {
  '男': 1,
  '女': 2,
  '未知': 0
};

// 患者组别映射
export const patientGroupMap = {
  '未分组': 0,
  '默认访问组': 1,
  '周三分组': 2,
  '周五分组': 3
};

// 医生映射
export const doctorMap = {
  '未知': 0,
  '蒋继宗': 1,
  '吴莹莹': 2,
  '袁逊': 3,
  '李杨': 4,
  '王静-张路教授': 5,
  '周磊-褚倩教授': 6,
  '朱颖莺-夏曙教授': 7,
  '杨昕-夏曙教授': 8,
  '肖晓光': 9,
  '张鹏': 10
};

export const symptomNameMapping: { [key: string]: string } = {
  q1: '疼痛',
  q2: '疲劳(乏力)',
  q3: '恶心',
  q4: '睡眠不安',
  q5: '苦恼',
  q6: '气短',
  q7: '健忘',
  q8: '胃口差',
  q9: '瞌睡',
  q10: '口干',
  q11: '悲伤感',
  q12: '呕吐',
  q13: '麻木感',
  q14: '咳嗽',
  q15: '便秘',
  q16: '嗓子疼',
  q17: '一般活动',
  q18: '情绪',
  q19: '工作',
  q20: '与他人关系',
  q21: '走路',
  q22: '生活乐趣',
}; 