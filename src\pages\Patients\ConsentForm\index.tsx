import { Modal, Button, Form, Select, Checkbox, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { connect } from 'dva';
import dayjs from 'dayjs';
import CreateSubject from '../ReserchSubject';
import { Dispatch } from 'umi';
import { ProjectOption } from '../../../pages/Patients/data';
import ElectronicSignature from '../ElectronicSignature/Doctors';
import { dataURLtoFile } from '@/utils/util';

interface ConsentFormProps {
  open: boolean;
  onCancel: () => void;
  onConfirm?: (values: any) => void;
  dispatch: Dispatch;
  loading?: boolean;
}

const ConsentForm: React.FC<ConsentFormProps> = (props) => {
  const {
    open,
    onCancel,
    onConfirm: onConsentFormConfirm,
    dispatch,
    loading = false
  } = props;
  const [form] = Form.useForm();
  const [isChecked, setIsChecked] = useState(false);


  // 项目选项
  const PROJECT_OPTIONS: ProjectOption[] = [
    {
      value: '1',
      label: '基于电子化患者报告结局的肺癌患者情绪压力及症状负担的相关性研究'
    },
    {
      value: '2',
      label: '同济基于ePRO肺癌根治性同步放化疗患者症状负担和症状群研究项目'
    }
  ];

  const [selectedProject, setSelectedProject] = React.useState('1');
  const [showCreateSubject, setShowCreateSubject] = React.useState(false);
  const [showSignature, setShowSignature] = useState(false);
  const [createdPatientId, setCreatedPatientId] = useState<number | null>(null);
  const [createdRecordId, setCreatedRecordId] = useState<number | null>(null);
  const [createSubjectKey, setCreateSubjectKey] = useState(0);
  const [signatureProcessKey, setSignatureProcessKey] = useState(0);

  useEffect(() => {
    if (open) {
      form.resetFields();
      setIsChecked(false);
      setSelectedProject('1');
      setShowCreateSubject(false);
      setShowSignature(false);
      setCreatedPatientId(null);
      setCreatedRecordId(null);
      setCreateSubjectKey(prevKey => prevKey + 1);
      setSignatureProcessKey(prevKey => prevKey + 1);
    }
  }, [open, form]);

  const handleSubmit = () => {
    form.validateFields()
      .then((values) => {
        if (!isChecked) {
          message.error('请先勾选"我已阅读知情同意书"');
          return;
        }
        // 转换日期格式
        const formattedValues = {
          ...values,
          signDate: dayjs(values.signDate).format('YYYY-MM-DD'),
          projectId: selectedProject,
          projectName: PROJECT_OPTIONS.find(p => p.value === selectedProject)?.label
        };
        console.log('提交的表单值:', formattedValues);

        // 显示创建研究对象卡片
        setShowCreateSubject(true);
      })
      .catch((error) => {
        console.error('表单验证错误:', error);
        message.error('请填写所有必填字段');
      });
  };

  // 处理创建研究对象完成后的回调
  const handleCreateSubjectFinish = async (values: any) => {
    console.log('创建研究对象表单数据:', values);
    // 假设 values 中有 recordId，通常是 medicalRecordNumber
    const recordId = values.recordId;
    if (!recordId) {
      message.error('研究对象病案号 (recordId) 缺失，无法继续流程');
      setShowCreateSubject(false);
      return;
    }
    setCreatedRecordId(recordId); // 保存 recordId

    try {
      // 第一步：创建研究对象
      const addResult: any = await dispatch({
        type: 'patients/addPatient',
        payload: values
      });

      console.log('从 patients/addPatient dispatch 收到的结果:', addResult);

      if (!(addResult && addResult.success)) {
        message.error(addResult?.message || '创建研究对象失败');
        setShowCreateSubject(false);
        return;
      }

      // 第二步：获取患者ID（独立于第一步）
      const fetchIdResult: any = await dispatch({
        type: 'patients/fetchPatientIdByRecordId',
        payload: { recordId: recordId }
      });

      console.log('从 patients/fetchPatientIdByRecordId dispatch 收到的结果:', fetchIdResult);

      if (fetchIdResult && fetchIdResult.success && fetchIdResult.patientId) {
        setCreatedPatientId(fetchIdResult.patientId);
        setShowCreateSubject(false); // 关闭研究对象创建模态框
        setShowSignature(true); // 显示电子签名（医生部分）
      } else {
        message.error(fetchIdResult?.message || '获取研究对象ID失败');
        setShowCreateSubject(false);
      }
    } catch (error) {
      console.error('处理研究对象创建过程中出错:', error);
      message.error('操作过程中发生错误，请重试');
      setShowCreateSubject(false);
    }
  };

  // 处理取消创建研究对象
  const handleCreateSubjectCancel = () => {
    setShowCreateSubject(false);
  };

  // 处理签名完成
  const handleDoctorSignatureConfirm = async (doctorSignature: string) => {
    if (!createdPatientId) {
      message.error('未能获取患者ID，无法上传医生签名');
      setShowSignature(false); // Close signature process if ID is missing
      onCancel(); // Close main modal
      return;
    }
    const doctorSignatureFile = dataURLtoFile(doctorSignature, 'doctor-signature.png');
    const success: any = await dispatch({
      type: 'patients/uploadSignature',
      payload: {
        file: doctorSignatureFile,
        patientId: createdPatientId,
        role: 1, // 医生
        cycleNumber: 0
      }
    });

    if (!success) {
      message.error('医生签名上传失败');
      // Decide if the flow should stop or allow patient signature anyway
      // For now, we proceed to patient signature as per Doctors.tsx logic
    }
  };

  const handlePatientSignatureConfirm = async (patientSignature: string) => {
    if (!createdPatientId) {
      message.error('未能获取患者ID，无法上传患者签名');
      setShowSignature(false);
      onCancel();
      return;
    }
    const patientSignatureFile = dataURLtoFile(patientSignature, 'patient-signature.png');
    const patientSuccess: any = await dispatch({
      type: 'patients/uploadSignature',
      payload: {
        file: patientSignatureFile,
        patientId: createdPatientId,
        role: 0, // 患者
        cycleNumber: 0
      }
    });

    if (patientSuccess) {
      message.success('患者签名上传成功');
      const pdfSuccess: any = await dispatch({
        type: 'patients/generatePDF',
        payload: { patientId: createdPatientId }
      });

      if (pdfSuccess) message.success('PDF生成请求已发送');
      else message.error('PDF生成请求失败');
    } else {
      message.error('患者签名上传失败');
    }
    setShowSignature(false); // Close ElectronicSignature component
    if (onConsentFormConfirm) { // Call the original onConfirm of ConsentForm
      onConsentFormConfirm({ patientId: createdPatientId, success: patientSuccess });
    }
    onCancel(); // Close ConsentForm modal
  };

  const handlePatientSignatureSkip = async () => {
    if (!createdPatientId) {
      message.error('未能获取患者ID，无法生成PDF');
      setShowSignature(false);
      onCancel();
      return;
    }
    message.info('患者已跳过签名');
    // Generate PDF even if patient skips, assuming doctor has signed.
    const pdfSuccess: any = await dispatch({
      type: 'patients/generatePDF',
      payload: { patientId: createdPatientId }
    });
    if (pdfSuccess) message.success('PDF生成请求已发送 (患者跳过)');
    else message.error('PDF生成请求失败 (患者跳过)');


    setShowSignature(false); // Close ElectronicSignature component
    if (onConsentFormConfirm) {
      onConsentFormConfirm({ patientId: createdPatientId, skipped: true });
    }
    onCancel(); // Close ConsentForm modal
  };

  // 处理整个签名流程的取消
  const handleSignatureProcessCancel = () => {
    setShowSignature(false); // Close ElectronicSignature
    message.info('签名流程已取消');
    // Do not call onCancel() for the main ConsentForm here,
    // as user might want to go back to ReserchSubject or ConsentForm details.
    // Let's call onCancel() to close the main form as well, simplifying the state.
    onCancel();
  };

  return (
    <>
      <Modal
        title="知情同意书"
        open={open && !showCreateSubject}
        width={800}
        footer={null}
        onCancel={onCancel}
        centered
      >
        <div style={{ marginBottom: 24 }}>
          <div style={{ marginBottom: 16 }}>
            <h4>请选择项目：</h4>
            <Select
              style={{ width: '100%' }}
              value={selectedProject}
              onChange={(value) => setSelectedProject(value)}
              options={PROJECT_OPTIONS}
              placeholder="请选择研究项目"
            />
          </div>
          <h3>项目名称：{PROJECT_OPTIONS.find(p => p.value === selectedProject)?.label}</h3>
          <h3>承担单位: 华中科技大学同济医学院附属同济医院</h3>
        </div>

        <div style={{ marginBottom: 24 }}>
          <h4>同意声明</h4>
          <p>
            我已经阅读了上述有关本研究的介绍，而且有机会就此项研究与医生讨论并提出问题。我提出的所有问题都得到了满意的答复。
          </p>
          <p>
            我知道参加本研究可能产生的风险和受益。我知晓参加研究是自愿的，我确认已有充足时间对此进行考虑，而且明白：
          </p>
          <ul>
            <li>我可以随时向医生咨询更多的信息。</li>
            <li>我可以随时退出本研究，而不会受到歧视或报复，医疗待遇与权益不会受到影响。</li>
          </ul>
          <p>我同意伦理委员会或管理部门查阅我的研究资料。</p>
          <p>我将获得一份经过签名并注明日期的知情同意书副本。</p>
          <p>最后，我决定同意参加本项研究，并保证尽量遵从医嘱。</p>
        </div>

        <div style={{ marginTop: 24, display: 'flex', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Checkbox
              checked={isChecked}
              onChange={(e) => setIsChecked(e.target.checked)}
            >
              我已阅读知情同意书
            </Checkbox>
          </div>
          <div>
            <Button style={{ marginRight: 16 }} onClick={onCancel}>
              返回
            </Button>
            <Button
              type="primary"
              onClick={handleSubmit}
              disabled={!isChecked}
              className="custom_toolbar_buttom"
            >
              确认
            </Button>
          </div>
        </div>
      </Modal>
      <Modal
        title="创建研究对象"
        open={showCreateSubject}
        width={800}
        footer={null}
        onCancel={handleCreateSubjectCancel}
        centered
        styles={{
          body: {
            maxHeight: '80vh',
            overflowY: 'auto',
            padding: '16px'
          }
        }}
      >
        <CreateSubject
          key={`create-subject-${createSubjectKey}`}
          onFinish={handleCreateSubjectFinish}
          onCancel={handleCreateSubjectCancel}
          projectId={selectedProject}
          projectName={PROJECT_OPTIONS.find(p => p.value === selectedProject)?.label}
        />
      </Modal>
      <ElectronicSignature
        key={`signature-process-${signatureProcessKey}`}
        open={showSignature}
        onCancel={handleSignatureProcessCancel}
        onDoctorConfirm={handleDoctorSignatureConfirm}
        onPatientConfirm={handlePatientSignatureConfirm}
        onPatientSkip={handlePatientSignatureSkip}
        patientId={createdPatientId}
      />
    </>
  );
};


const mapStateToProps = ({ patients, loading }: any) => {
  return {
    loading: loading.effects['patients/addPatient'] || loading.effects['patients/uploadSignature'] || loading.effects['patients/generatePDF'],
  };
};

export default connect(mapStateToProps)(ConsentForm);