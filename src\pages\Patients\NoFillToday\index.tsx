import React, { useEffect, useMemo, useState } from 'react';
import { ProList } from '@ant-design/pro-components';
import { Typography, Spin, Input, ConfigProvider, Button } from 'antd';
import { connect } from 'dva';
import { ExpiredScheduleItem } from '../data.d';

const { Title } = Typography;

interface NoFillTodayProps {
  dispatch: any;
  expiredSchedules: ExpiredScheduleItem[];
  loading: boolean;
}

const NoFillToday: React.FC<NoFillTodayProps> = ({ dispatch, expiredSchedules, loading }) => {
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    dispatch({
      type: 'patients/fetchExpiredSchedule',
    });
  }, [dispatch]);

  const generateFrequencyName = (item: ExpiredScheduleItem) => {
    const { projectName, cycleNumber = 0 } = item;
    if (projectName && projectName.includes('基于电子化患者报告结局的肺癌患者情绪压力及症状负担的相关性研究')) {
      if (cycleNumber === 0) {
        return '基线日期';
      }
      const weekNumber = cycleNumber * 4;
      return `第${weekNumber}周的第${cycleNumber}次随访`;
    }
    return `周期${cycleNumber}`;
  };

  const dataSource = useMemo(() => {
    if (!expiredSchedules) {
      return [];
    }
    let data = [...expiredSchedules];

    data.sort((a, b) => b.patientId - a.patientId);

    if (searchTerm) {
      data = data.filter(item => 
        item.patientId.toString().includes(searchTerm)
      );
    }

    return data.map(schedule => ({
      ...schedule,
      frequency: generateFrequencyName(schedule),
    }));
  }, [expiredSchedules, searchTerm]);

  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        background: 'white',
        padding: 16,
        borderRadius: 8,
        boxShadow: '0 1px 2px 0 rgba(0,0,0,0.03)',
        WebkitUserSelect: 'none',
        MozUserSelect: 'none',
      }}
    >
      <Title
        level={5}
        style={{
          marginBottom: 16,
          paddingLeft: 32,
          color: '#39bbdb',
        }}
      >
        今日未填研究对象列表
      </Title>
      <Spin spinning={loading}>
        <ConfigProvider
          theme={{
            components: {
              Input: {
                colorPrimary: '#39bbdb',
                colorPrimaryHover: '#39bbdb',
              },
            },
          }}
        >
          <ProList
            rowKey={(record) => `${record.patientId}-${record.cycleNumber}`}
            dataSource={dataSource}
            style={{
              width: '100%',
              minHeight: 400,
              userSelect: 'none',
            }}
            toolBarRender={() => [
              <Input.Search
                key="search"
                placeholder="按 RecordID 检索"
                onSearch={(value) => setSearchTerm(value.trim())}
                onChange={(e) => {
                  if (e.target.value === '') {
                    setSearchTerm('');
                  }
                }}
                onPressEnter={(e) => setSearchTerm((e.target as HTMLInputElement).value.trim())}
                style={{ width: 240, marginRight: 8 }}
              />,
              <Button key="reset" onClick={() => setSearchTerm('')}>
                重置
              </Button>,
            ]}
            pagination={{
              pageSize: 6,
              showSizeChanger: false,
              itemRender: (page, type, originalElement) => {
                if (!originalElement) return null;
                return React.cloneElement(originalElement as React.ReactElement, {
                  style: {
                    pointerEvents: 'auto',
                    userSelect: 'text',
                    cursor: 'pointer',
                    ...(type === 'page' ? {
                      borderColor: '#39bbdb',
                    } : {}),
                  },
                });
              }
            }}
            showActions="hover"
            metas={{
              title: {
                dataIndex: 'name',
                render: (_, record) => (
                  <div style={{ paddingLeft: 32, userSelect: 'none' }}>
                    <span style={{ marginRight: 8 }}>{record.name}</span>
                    <span style={{ color: '#727272', marginRight: 16 }}>
                      项目：{record.projectName}
                    </span>
                    <span style={{ color: '#727272', marginRight: 16 }}>
                      RecordID：{record.patientId}
                    </span>
                    <span style={{ marginRight: 16 }}>电话：{record.phone}</span>
                    <span style={{ color: '#727272' }}>
                      应答频次：{record.frequency}
                    </span>
                  </div>
                ),
              },
            }}
            search={false}
          />
        </ConfigProvider>
      </Spin>
    </div>
  );
};

const mapStateToProps = ({ patients, loading }: any) => ({
  expiredSchedules: patients.expiredSchedules,
  loading: loading.effects['patients/fetchExpiredSchedule'],
});

export default connect(mapStateToProps)(NoFillToday);
