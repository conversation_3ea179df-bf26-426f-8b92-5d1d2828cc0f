import React, { useEffect } from 'react';
import { ProLayout } from '@ant-design/pro-layout';
import { Outlet, useLocation, history } from 'umi';
import { Avatar, Dropdown, Menu, message } from 'antd';
import { LogoutOutlined, UserOutlined } from '@ant-design/icons';
import styles from './styles.less';
import "./styles.less"
import storeUtil from '@/utils/storeUtil'; 

const menuItems = [
  { path: '/patients', name: '患者管理' },
  { path: '/quality', name: '项目质控' },
  { path: '/calendar', name: 'PRO日历' },
];

const Layouts: React.FC = () => {
  const location = useLocation();

  useEffect(() => {

    const tokenData = storeUtil.get('token');
    const isAuthenticated = !!(tokenData && tokenData.status === storeUtil.status.SUCCESS && tokenData.value);

    if (!isAuthenticated && location.pathname !== '/login') {
      message.error('请先登录后再访问！'); // 添加提示信息
      history.push('/login');
    }
  }, [location.pathname]); // 当路径改变时重新运行此 effect

  if (location.pathname === '/login') {
    // 如果是登录页，则只渲染 Outlet，不显示主布局
    return <Outlet />;
  }

  // 如果在非登录页面但 token 不存在（理论上 useEffect 会处理跳转，但作为防御性检查）
  // 确保在跳转完成前不渲染主布局，或者可以显示一个加载状态
  // 不过，通常 useEffect 的跳转会很快发生
  // if (!localStorage.getItem('token') && location.pathname !== '/login') {
  const tokenDataForCheck = storeUtil.get('token');
  if (!(tokenDataForCheck && tokenDataForCheck.status === storeUtil.status.SUCCESS && tokenDataForCheck.value) && location.pathname !== '/login') {
    return null; // 或者一个加载指示器，等待 useEffect 跳转
  }

  const onMenuClick = ({ key }: { key: string }) => {
    if (key === 'logout') {
      // localStorage.removeItem('token'); // 退出登录时清除 token -> 改用 storeUtil
      storeUtil.remove('token');
      history.push('/login');
    }
  };

  const items = [
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: onMenuClick,
    },
  ];

  return (
    <ProLayout
      layout="top"
      fixedHeader
      route={{ routes: menuItems }}
      location={{ pathname: location.pathname }}
      logo={false}
      headerTitleRender={() => (
        <div className={styles['header-title']}>患者随访全病程管理平台</div>
      )}
      avatarProps={{
        src: <Avatar icon={<UserOutlined />} />,
        render: () => (
          <Dropdown menu={{ items }} placement="bottomRight">
            <div
              className={styles['user-dropdown']}
              style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
            >
              <Avatar icon={<UserOutlined />} />
              <span style={{ marginLeft: 8, color: '#333' }}>用户</span>
            </div>
          </Dropdown>
        ),
      }}
      menuProps={{
        mode: 'horizontal',
      }}
      headerContentRender={() => (
        <div className={styles['header-container']}>
          <Menu 
            mode="horizontal" 
            selectedKeys={[location.pathname]}
            className={styles['centered-menu']}
          >
            {menuItems.map(item => (
              <Menu.Item 
                key={item.path} 
                onClick={() => history.push(item.path)}
              >
                {item.name}
              </Menu.Item>
            ))}
          </Menu>
        </div>
      )}
      actionsRender={() => []}
      contentStyle={{ padding: 24 }}
    >
      <Outlet />
    </ProLayout>
  );
};

export default Layouts;