import React, { useRef } from 'react';
import { <PERSON><PERSON>, But<PERSON>, Tabs, Spin, message } from 'antd';
import ESDemoinformatics from './Project1/ESDemoinformatics';
import ESDiseaseInfo from './Project1/ESDiseaseInfo';
import RCDemoinformatics from './Project2/RCDemoinformatics';
import RCDiseaseInfo from './Project2/RCDiseaseInfo';
import './styles.less';

const { TabPane } = Tabs;

interface EcrfModalProps {
  visible: boolean;
  record: any;
  onClose: () => void;
  loading: boolean;
  activeTab: 'demographics' | 'disease';
  onTabChange: (activeTab: 'demographics' | 'disease') => void;
  demographicsData: any;
  diseaseData: any;
  projectType: string;
  onDemographicsSubmit: (formData: any) => Promise<boolean>;
  onDiseaseSubmit: (formData: any) => Promise<boolean>;
  onSaveAll: (demographicsFormData: any, diseaseFormData: any) => Promise<boolean>;
}

const EcrfModal: React.FC<EcrfModalProps> = ({
  visible,
  record,
  onClose,
  loading,
  activeTab,
  onTabChange,
  demographicsData,
  diseaseData,
  projectType,
  onDemographicsSubmit,
  onDiseaseSubmit,
  onSaveAll,
}) => {
  const demographicsFormRef = useRef<any>(null);
  const diseaseFormRef = useRef<any>(null);

  // 处理保存按钮点击 - 同时保存两个表单
  const handleSave = async () => {
    try {
      let demographicsSuccess = true;
      let diseaseSuccess = true;

      // 保存人口信息学表单
      if (demographicsFormRef.current && demographicsFormRef.current.submit) {
        demographicsSuccess = await demographicsFormRef.current.submit();
      }

      // 保存疾病信息表单
      if (diseaseFormRef.current && diseaseFormRef.current.submit) {
        diseaseSuccess = await diseaseFormRef.current.submit();
      }

      // 根据保存结果给出提示
      if (demographicsSuccess && diseaseSuccess) {
        message.success('所有数据保存成功');
        onClose();
      } else if (demographicsSuccess || diseaseSuccess) {
        message.warning('部分数据保存成功，请检查并重试失败的部分');
      } else {
        message.error('数据保存失败，请重试');
      }
    } catch (error) {
      console.error('保存过程中发生错误:', error);
      message.error('保存失败，请检查网络连接');
    }
  };

  // 渲染人口信息学表单
  const renderDemographicsForm = () => {
    if (projectType === '1') {
      return (
        <ESDemoinformatics
          ref={demographicsFormRef}
          initialData={demographicsData}
          patientId={record.id}
          onSubmit={onDemographicsSubmit}
        />
      );
    } else if (projectType === '2') {
      return (
        <RCDemoinformatics
          ref={demographicsFormRef}
          initialData={demographicsData}
          patientId={record.id}
          onSubmit={onDemographicsSubmit}
        />
      );
    }
    return null;
  };

  // 渲染疾病信息表单
  const renderDiseaseForm = () => {
    if (projectType === '1') {
      return (
        <ESDiseaseInfo
          ref={diseaseFormRef}
          initialData={diseaseData}
          patientId={record.id}
          onSubmit={onDiseaseSubmit}
        />
      );
    } else if (projectType === '2') {
      return (
        <RCDiseaseInfo
          ref={diseaseFormRef}
          initialData={diseaseData}
          patientId={record.id}
          onSubmit={onDiseaseSubmit}
        />
      );
    }
    return null;
  };

  return (
    <Modal
      title={<span style={{ color: '#39bbdb' }}>eCRF - {record.name}</span>}
      open={visible}
      onCancel={onClose}
      width={1000}
      style={{ maxHeight: '80vh' }}
      footer={[
        <Button key="cancel" onClick={onClose}>
          取消
        </Button>,
        <Button
          key="save"
          type="primary"
          style={{
            background: '#39bbdb',
            borderColor: '#39bbdb',
            color: '#fff',
          }}
          loading={loading}
          onClick={handleSave}
        >
          保存
        </Button>,
      ]}
      closable={true}
      closeIcon={<span style={{ fontSize: '18px', fontWeight: 'bold' }}>×</span>}
    >
      {loading ? (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" tip="正在加载数据..." />
        </div>
      ) : (
        <Tabs
          activeKey={activeTab}
          onChange={(key) => onTabChange(key as 'demographics' | 'disease')}
          type="card"
          className="custom-ecrf-tabs"
          style={{ marginTop: '16px' }}
        >
          <TabPane tab="人口信息学" key="demographics">
            <div style={{ maxHeight: '60vh', overflow: 'auto', padding: '16px 0' }}>
              {renderDemographicsForm()}
            </div>
          </TabPane>
          <TabPane tab="疾病信息表单" key="disease">
            <div style={{ maxHeight: '60vh', overflow: 'auto', padding: '16px 0' }}>
              {renderDiseaseForm()}
            </div>
          </TabPane>
        </Tabs>
      )}
    </Modal>
  );
};

export default EcrfModal;