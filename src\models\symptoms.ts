import { Effect, Reducer } from 'umi';
import { message } from 'antd';
import { getSymptomDataByPatientId } from '@/services/symptoms';
import { SymptomRecord, GetSymptomDataParams } from '@/pages/Symptoms/data';

export interface SymptomsModelState {
  symptomData: SymptomRecord[];
  loading: boolean;
}

export interface SymptomsModelType {
  namespace: 'symptoms';
  state: SymptomsModelState;
  effects: {
    fetchSymptomData: Effect;
  };
  reducers: {
    saveSymptomData: Reducer<SymptomsModelState>;
    setLoading: Reducer<SymptomsModelState>;
  };
}

const SymptomsModel: SymptomsModelType = {
  namespace: 'symptoms',
  state: {
    symptomData: [],
    loading: false,
  },
  effects: {
    *fetchSymptomData({ payload }: any, { call, put }) {
      yield put({ type: 'setLoading', payload: true });
      try {
        const response = yield call(getSymptomDataByPatientId, payload);
        if (response && response.code === 0) {
          yield put({
            type: 'saveSymptomData',
            payload: response.data || [],
          });
        } else {
          message.error(response?.message || '获取症状数据失败');
          yield put({ type: 'saveSymptomData', payload: [] });
        }
      } catch (error) {
        console.error('获取症状数据出错:', error);
        message.error('获取症状数据失败，请检查网络连接');
        yield put({ type: 'saveSymptomData', payload: [] });
      } finally {
        yield put({ type: 'setLoading', payload: false });
      }
    },
  },
  reducers: {
    saveSymptomData(state, { payload }) {
      return { ...state, symptomData: payload };
    },
    setLoading(state, { payload }) {
      return { ...state, loading: payload };
    },
  },
};

export default SymptomsModel;
