{"private": true, "author": "dcf", "scripts": {"dev": "cross-env REACT_APP_ENV=develop UMI_ENV=develop umi dev", "build": "umi build", "postinstall": "umi setup", "setup": "umi setup", "start": "npm run dev"}, "dependencies": {"@ant-design/colors": "^8.0.0", "@ant-design/icons": "^6.0.0", "@ant-design/pro-components": "^2.8.7", "@types/js-cookie": "^3.0.6", "@umijs/max": "^4.4.10", "antd": "^5.24.6", "axios": "^1.8.4", "cross-env": "7.0.3", "dva": "^2.4.1", "echarts": "^5.6.0", "js-cookie": "^3.0.5", "lerna": "^4.0.0", "less": "^4.3.0", "react-signature-canvas": "^1.1.0-alpha.2", "umi": "^4.4.6", "umi-request": "^1.4.0", "uuid": "^11.1.0"}, "devDependencies": {"@types/react": "^18.0.33", "@types/react-dom": "^18.0.11", "@types/uuid": "^10.0.0", "@umijs/plugins": "^4.4.6", "typescript": "^5.0.3"}}