import { Effect, Reducer } from 'umi';
import { login, LoginResult } from '@/services/login';
import { history } from 'umi';
import { message } from 'antd';
import storeUtil from '@/utils/storeUtil'; 

export interface LoginState {
  data?: string;
  correct: boolean;
}

export interface LoginType {
  namespace: 'login';
  state: LoginState;
  effects: {
    login: Effect;
  };
  reducers: {
    saveLoginStatus: Reducer<LoginState>;
  };
}

const LoginModel: LoginType = {
  namespace: 'login',
  
  state: {
    data: undefined,
    correct: true,
  },
  
  effects: {
    *login({ payload }, { call, put }) {
      try {
        const response: LoginResult = yield call(login, payload);
        
        if (response.code === 0) {
          yield put({
            type: 'saveLoginStatus',
            payload: {
              data: response.data,
              correct: true,
            },
          });
          
          // 保存token到localStorage -> 改用 storeUtil
          // localStorage.setItem('token', response.data);
          // storeUtil 默认过期时间为一周，您可以根据需要传递第三个参数设置过期时间
          storeUtil.set('token', response.data); 
          
          // 登录成功提示
          message.success(response.message || '登录成功');
          
          // 跳转到首页
          history.push('/patients');
        } else {
          // 登录失败提示
          message.error(response.message || '登录失败');
          
          yield put({
            type: 'saveLoginStatus',
            payload: {
              data: undefined,
              correct: false,
            },
          });
        }
      } catch (error) {
        message.error('登录请求失败，请稍后再试');
        console.error(error);
        yield put({
          type: 'saveLoginStatus',
          payload: {
            data: undefined,
            correct: false,
          },
        });
      }
    },
  },
  
  reducers: {
    saveLoginStatus(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};

export default LoginModel;