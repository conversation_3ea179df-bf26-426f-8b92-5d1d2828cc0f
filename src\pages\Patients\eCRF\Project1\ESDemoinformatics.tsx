// 基于电子化患者报告结局的晚期肺癌患者情绪压力及症状负担与脑转移状态相关性的研究放化疗人口信息学
import React, { useEffect, useImperativeHandle, forwardRef } from 'react';
import locale from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import { Form, Input, Select, Radio, InputNumber, DatePicker, Divider, ConfigProvider } from 'antd';
import 'dayjs/locale/zh-cn';
import FormItemWithHistory from '../History/FormItemWithHistory';

dayjs.locale('zh-cn');

const { Option } = Select;

interface ESDemoinformaticsProps {
    initialData?: any;
    onSubmit?: (formData: any) => Promise<boolean>;
    patientId?: number;
}

interface ESDemoinformaticsRef {
    submit: () => Promise<boolean>;
}

const ESDemoinformatics = forwardRef<ESDemoinformaticsRef, ESDemoinformaticsProps>(({ 
  initialData, 
  onSubmit,
  patientId 
}, ref) => {
    const [form] = Form.useForm();

    useEffect(() => {
        if (initialData) {
            // 转换数据格式并填充表单
            const formData = {
                name: initialData.name,
                gender: initialData.gender,
                birthDate: initialData.birthDate ? dayjs(initialData.birthDate) : null,
                age: initialData.age,
                ethnic: initialData.ethnic,
                otherEthnic: initialData.otherEthnic,
                maritalStatus: initialData.maritalStatus,
                degree: initialData.degree,
                height: initialData.height,
                weight: initialData.weight,
                income: initialData.income,
                BMI: initialData.height && initialData.weight ? 
                    (initialData.weight / Math.pow(initialData.height / 100, 2)).toFixed(2) : '',
                career: initialData.career,
                insurance: initialData.insurance ? String(initialData.insurance).split(',') : [],
                smokingStatus: initialData.smokingStatus,
                smokingTime: initialData.smokingTime,
                drink: initialData.drink,
                drinkTime: initialData.drinkTime,
                hypertension: initialData.hypertension,
                hypertensionTime: initialData.hypertensionTime,
                diabetes: initialData.diabetes,
                diabetesTime: initialData.diabetesTime,
                otherDisease: initialData.otherDisease,
                otherDiseaseName: initialData.otherDiseaseName,
                surveyMethod: initialData.surveyMethod,
                identity: initialData.identity,
                investigatorName: initialData.investigatorName,
                investigateDate: initialData.investigateDate ? dayjs(initialData.investigateDate) : null,
                answerStatue: initialData.answerStatue,
            };
            form.setFieldsValue(formData);
        }
    }, [initialData, form]);

    // 计算BMI
    const calculateBMI = () => {
        const height = form.getFieldValue('height');
        const weight = form.getFieldValue('weight');
        if (height && weight) {
            const bmi = (weight / Math.pow(height / 100, 2)).toFixed(2);
            form.setFieldsValue({ BMI: bmi });
        }
    };

    // 处理表单提交
    const handleSubmit = async (): Promise<boolean> => {
      if (!patientId || !onSubmit) {
        return false;
      }

      try {
        const values = await form.validateFields();
        
        // 处理数据格式，转换成API需要的格式
        const submitData = {
          patientId: patientId,
          name: values.name,
          birthDate: values.birthDate ? values.birthDate.format('YYYY-MM-DD') : undefined,
          age: values.age,
          gender: values.gender,
          ethnic: values.ethnic,
          otherEthnic: values.otherEthnic,
          maritalStatus: values.maritalStatus,
          degree: values.degree,
          height: values.height,
          weight: values.weight,
          income: values.income,
          career: values.career,
          insurance: Array.isArray(values.insurance) ? values.insurance[0] : values.insurance, // 取第一个值或原值
          smokingStatus: values.smokingStatus,
          smokingTime: values.smokingTime,
          drink: values.drink,
          drinkTime: values.drinkTime,
          hypertension: values.hypertension,
          hypertensionTime: values.hypertensionTime,
          diabetes: values.diabetes,
          diabetesTime: values.diabetesTime,
          otherDisease: values.otherDisease,
          otherDiseaseName: values.otherDiseaseName,
          surveyMethod: values.surveyMethod,
          identity: values.identity,
          investigatorName: values.investigatorName,
          investigateDate: values.investigateDate ? values.investigateDate.format('YYYY-MM-DD') : undefined,
          answerStatue: values.answerStatue,
        };

        return await onSubmit(submitData);
      } catch (error) {
        console.error('表单验证失败:', error);
        return false;
      }
    };

    // 使用useImperativeHandle暴露submit方法给父组件
    useImperativeHandle(ref, () => ({
      submit: handleSubmit,
    }));

    // 映射值到选项
    const ethnicOptions = ['汉族', '土家族', '苗族', '彝族', '藏族', '羌族', '回族', '其它', '未知'];
    const maritalOptions = ['未婚', '已婚', '离异', '丧偶', '分居', '其他', '未知'];
    const degreeOptions = ['文盲', '小学', '中学', '高中', '大专', '本科及以上'];
    const careerOptions = ['农民', '工人', '机关事业单位', '企业', '离退休', '其他', '无业', '未知'];
    const insuranceOptions = ['无', '城镇职工医保', '城镇居民医保', '农村居民医保', '商业保险', '公费医疗', '其他', '未知'];
    const statusOptions = ['未完成', '待检验', '已完成'];

    return (
        <ConfigProvider
            locale={locale}
            theme={{
                components: {
                    Input: {
                        activeBorderColor: '#39bbdb',
                        hoverBorderColor: '#39bbdb',
                        borderRadius: 4,
                        activeShadow: '0 0 0 2px rgba(24, 144, 255, 0.2)',
                    },
                    Select: {
                        activeBorderColor: '#39bbdb',
                        hoverBorderColor: '#39bbdb',
                        borderRadius: 4,
                        controlOutline: '0 0 0 2px rgba(24, 144, 255, 0.2)',
                        optionSelectedBg: '#e6f7ff',
                    },
                    InputNumber: {
                        activeBorderColor: '#39bbdb',
                        hoverBorderColor: '#39bbdb',
                        borderRadius: 4,
                        activeShadow: '0 0 0 2px rgba(24, 144, 255, 0.2)'
                    },
                    DatePicker: {
                        activeBorderColor: '#39bbdb',
                        hoverBorderColor: '#39bbdb',
                        borderRadius: 4,
                        activeShadow: '0 0 0 2px rgba(24, 144, 255, 0.2)'
                    },
                    Radio: {
                        colorPrimary: '#39bbdb',
                    },
                },
            }}
        >
            <Form form={form} layout="vertical">
                <FormItemWithHistory
                    name="name"
                    label="姓名"
                    rules={[{ required: true, message: '请输入姓名' }]}
                    fieldName="name"
                    fieldLabel="姓名"
                    patientId={patientId}
                    project={0}
                >
                    <Input placeholder="请输入姓名" />
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="gender"
                    label="性别"
                    rules={[{ required: true, message: '请输入性别' }]}
                    fieldName="gender"
                    fieldLabel="性别"
                    patientId={patientId}
                    project={0}
                >
                    <Radio.Group>
                        <Radio value={1}>男</Radio>
                        <Radio value={2}>女</Radio>
                    </Radio.Group>
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="birthDate"
                    label="出生年月"
                    rules={[{ required: true, message: '请输入出生年月' }]}
                    fieldName="birthDate"
                    fieldLabel="出生年月"
                    patientId={patientId}
                    project={0}
                >
                    <DatePicker style={{ width: '100%' }} />
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="investigateDate"
                    label="调查日期"
                    rules={[{ required: true, message: '请输入调查日期' }]}
                    fieldName="investigateDate"
                    fieldLabel="调查日期"
                    patientId={patientId}
                    project={0}
                >
                    <DatePicker style={{ width: '100%' }} />
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="age"
                    label="患病年龄"
                    fieldName="age"
                    fieldLabel="患病年龄"
                    patientId={patientId}
                    project={0}
                >
                    <Input placeholder="请输入文本" />
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="ethnic"
                    label="民族"
                    fieldName="ethnic"
                    fieldLabel="民族"
                    patientId={patientId}
                    project={0}
                >
                    <Select placeholder="请选择">
                        {ethnicOptions.map((item, index) => (
                            <Option key={index} value={index}>{item}</Option>
                        ))}
                    </Select>
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="otherEthnic"
                    label="民族-其他"
                    fieldName="otherEthnic"
                    fieldLabel="民族-其他"
                    patientId={patientId}
                    project={0}
                >
                    <Input placeholder="请输入其他民族" />
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="maritalStatus"
                    label="婚姻状况"
                    fieldName="maritalStatus"
                    fieldLabel="婚姻状况"
                    patientId={patientId}
                    project={0}
                >
                    <Select placeholder="请选择">
                        {maritalOptions.map((item, index) => (
                            <Option key={index} value={index}>{item}</Option>
                        ))}
                    </Select>
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="degree"
                    label="学历"
                    fieldName="degree"
                    fieldLabel="学历"
                    patientId={patientId}
                    project={0}
                >
                    <Select placeholder="请选择">
                        {degreeOptions.map((item, index) => (
                            <Option key={index} value={index}>{item}</Option>
                        ))}
                    </Select>
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="height"
                    label="身高 (cm)"
                    fieldName="height"
                    fieldLabel="身高 (cm)"
                    patientId={patientId}
                    project={0}
                >
                    <InputNumber min={0} style={{ width: '100%' }} onChange={calculateBMI} />
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="weight"
                    label="体重 (kg)"
                    fieldName="weight"
                    fieldLabel="体重 (kg)"
                    patientId={patientId}
                    project={0}
                >
                    <InputNumber min={0} style={{ width: '100%' }} onChange={calculateBMI} />
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="income"
                    label="平均月收入"
                    fieldName="income"
                    fieldLabel="平均月收入"
                    patientId={patientId}
                    project={0}
                >
                    <Radio.Group>
                        <Radio value={0}>＜800</Radio>
                        <Radio value={1}>800-1500</Radio>
                        <Radio value={2}>1500-3000</Radio>
                        <Radio value={3}>3000-5000</Radio>
                        <Radio value={4}>＞5000</Radio>
                        <Radio value={5}>未知</Radio>
                    </Radio.Group>
                </FormItemWithHistory>

                <Form.Item name="BMI" label="BMI体重指数">
                    <Input placeholder="由其他字段计算得出" disabled />
                </Form.Item>

                <FormItemWithHistory
                    name="career"
                    label="职业"
                    fieldName="career"
                    fieldLabel="职业"
                    patientId={patientId}
                    project={0}
                >
                    <Select placeholder="请选择">
                        {careerOptions.map((item, index) => (
                            <Option key={index} value={index}>{item}</Option>
                        ))}
                    </Select>
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="insurance"
                    label="医疗保险类型（可多选）"
                    fieldName="insurance"
                    fieldLabel="医疗保险类型"
                    patientId={patientId}
                    project={0}
                >
                    <Select mode="multiple" placeholder="请选择">
                        {insuranceOptions.map((item, index) => (
                            <Option key={index} value={index}>{item}</Option>
                        ))}
                    </Select>
                </FormItemWithHistory>

                <Divider />

                <FormItemWithHistory
                    name="smokingStatus"
                    label="吸烟史"
                    fieldName="smokingStatus"
                    fieldLabel="吸烟史"
                    patientId={patientId}
                    project={0}
                >
                    <Radio.Group>
                        <Radio value={0}>目前抽烟</Radio>
                        <Radio value={1}>已戒烟</Radio>
                        <Radio value={2}>从不抽烟</Radio>
                    </Radio.Group>
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="smokingTime"
                    label="吸烟年数（年）"
                    fieldName="smokingTime"
                    fieldLabel="吸烟年数（年）"
                    patientId={patientId}
                    project={0}
                >
                    <InputNumber min={0} style={{ width: '100%' }} />
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="drink"
                    label="目前是否饮酒（持续饮酒1年以上）"
                    fieldName="drink"
                    fieldLabel="目前是否饮酒"
                    patientId={patientId}
                    project={0}
                >
                    <Radio.Group>
                        <Radio value={0}>否</Radio>
                        <Radio value={1}>是</Radio>
                        <Radio value={2}>已戒酒</Radio>
                        <Radio value={3}>未知</Radio>
                    </Radio.Group>
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="drinkTime"
                    label="饮酒年数（年）"
                    fieldName="drinkTime"
                    fieldLabel="饮酒年数（年）"
                    patientId={patientId}
                    project={0}
                >
                    <InputNumber min={0} style={{ width: '100%' }} />
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="hypertension"
                    label="是否有高血压？"
                    fieldName="hypertension"
                    fieldLabel="是否有高血压"
                    patientId={patientId}
                    project={0}
                >
                    <Radio.Group>
                        <Radio value={0}>否</Radio>
                        <Radio value={1}>是</Radio>
                    </Radio.Group>
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="hypertensionTime"
                    label="高血压史年数（年）"
                    fieldName="hypertensionTime"
                    fieldLabel="高血压史年数（年）"
                    patientId={patientId}
                    project={0}
                >
                    <InputNumber min={0} style={{ width: '100%' }} />
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="diabetes"
                    label="是否有糖尿病？"
                    fieldName="diabetes"
                    fieldLabel="是否有糖尿病"
                    patientId={patientId}
                    project={0}
                >
                    <Radio.Group>
                        <Radio value={0}>否</Radio>
                        <Radio value={1}>是</Radio>
                    </Radio.Group>
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="diabetesTime"
                    label="糖尿病史年数（年）"
                    fieldName="diabetesTime"
                    fieldLabel="糖尿病史年数（年）"
                    patientId={patientId}
                    project={0}
                >
                    <InputNumber min={0} style={{ width: '100%' }} />
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="otherDisease"
                    label="是否有其它疾病？"
                    fieldName="otherDisease"
                    fieldLabel="是否有其它疾病"
                    patientId={patientId}
                    project={0}
                >
                    <Radio.Group>
                        <Radio value={0}>否</Radio>
                        <Radio value={1}>是</Radio>
                    </Radio.Group>
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="otherDiseaseName"
                    label="其它疾病名称"
                    fieldName="otherDiseaseName"
                    fieldLabel="其它疾病名称"
                    patientId={patientId}
                    project={0}
                >
                    <Input placeholder="请输入文本" />
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="surveyMethod"
                    label="本次调查方式"
                    fieldName="surveyMethod"
                    fieldLabel="本次调查方式"
                    patientId={patientId}
                    project={0}
                >
                    <Radio.Group>
                        <Radio value={0}>面对面调查</Radio>
                        <Radio value={1}>电话调查</Radio>
                        <Radio value={2}>其他</Radio>
                    </Radio.Group>
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="identity"
                    label="本次作答人员"
                    fieldName="identity"
                    fieldLabel="本次作答人员"
                    patientId={patientId}
                    project={0}
                >
                    <Radio.Group>
                        <Radio value={0}>患者本人</Radio>
                        <Radio value={1}>家属代答</Radio>
                        <Radio value={2}>其他</Radio>
                    </Radio.Group>
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="investigatorName"
                    label="调查员姓名"
                    fieldName="investigatorName"
                    fieldLabel="调查员姓名"
                    patientId={patientId}
                    project={0}
                >
                    <Input placeholder="请输入文本" />
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="answerStatue"
                    fieldName="answerStatue"
                    fieldLabel="答题状态"
                    patientId={patientId}
                    project={0}
                >
                    <Select placeholder="请选择">
                        {statusOptions.map((item, index) => (
                            <Option key={index} value={index}>{item}</Option>
                        ))}
                    </Select>
                </FormItemWithHistory>
            </Form>
        </ConfigProvider>
    );
});

ESDemoinformatics.displayName = 'ESDemoinformatics';

export default ESDemoinformatics;
