import { Effect, Reducer } from 'umi';
import { message } from 'antd';
import { addPatient,
   getPatientList, 
   updatePatient, 
   uploadSignature, 
   generatePDF,
   previewPDF,
   findPatientIdByRecordId,
   showDemographicsEmoSymCorr,
   showDemographicsRadSymBur,
   showDiseaseInformationEmoSymCorr,
   showDiseaseInformationRadSymBur,
   updateDemographicsEmoSymCorr,
   updateDiseaseInformationEmoSymCorr,
   updateDemographicsRadSymBur,
   updateDiseaseInformationRadSymBur,
   listHistory,
   addCtcae,
   listCtcae,
   deletePatient,
   getScheduleList,
   answerProjectEmo,
   getAnswerProjectEmo,
   getExpiredSchedule
  } from '@/services/patients';
import { PatientAddParams, 
  PatientListParams, 
  PatientUpdateParams,
  FindPatientIdByRecordIdParams,
  PreviewPDFParams,
} from '@/pages/Patients/data';

export interface PatientsModelState {
  loading: boolean;
  patientList: any[];
  total: number;
  expiredSchedules: any[];
}

export interface PatientsModelType {
  namespace: 'patients';
  state: PatientsModelState;
  effects: {
    addPatient: Effect;
    fetchPatientList: Effect;
    updatePatient: Effect;
    uploadSignature: Effect;
    generatePDF: Effect;
    previewPDF: Effect;
    fetchPatientIdByRecordId: Effect;
    fetchDemographicsEmoSymCorr: Effect;
    fetchDemographicsRadSymBur: Effect;
    fetchDiseaseInformationEmoSymCorr: Effect;
    fetchDiseaseInformationRadSymBur: Effect;
    updateDemographicsEmoSymCorr: Effect;
    updateDiseaseInformationEmoSymCorr: Effect;
    updateDemographicsRadSymBur: Effect;
    updateDiseaseInformationRadSymBur: Effect;
    fetchFieldHistory: Effect;
    addCtcae: Effect;
    fetchCtcaeList: Effect;
    deletePatient: Effect;
    fetchScheduleList: Effect;
    answerProjectEmo: Effect;
    fetchAnswerProjectEmo: Effect;
    fetchExpiredSchedule: Effect;
  };
  reducers: {
    setLoading: Reducer<PatientsModelState>;
    setPatientList: Reducer<PatientsModelState>;
    setCurrentDemographics: Reducer<PatientsModelState>;
    setExpiredSchedules: Reducer<PatientsModelState>;
  };
}

const PatientsModel: PatientsModelType = {
  namespace: 'patients',
  
  state: {
    loading: false,
    patientList: [],
    total: 0,
    expiredSchedules: [],
  },
  
  effects: {
    *addPatient({ payload }, { call, put }) {
      yield put({ type: 'setLoading', payload: true });
      try {
        const response = yield call(addPatient, payload);
        if (response && response.code === 0) {
          message.success('研究对象创建成功');
          return {
            success: true,
            recordId: response.data?.patientId || null
          };
        } else {
          message.error(response?.message || '创建失败，请重试');
          return {
            success: false
          };
        }
      } catch (error) {
        console.error('创建研究对象出错:', error);
        message.error('创建失败，请检查网络连接');
        return {
          success: false
        };
      } finally {
        yield put({ type: 'setLoading', payload: false });
      }
    },
    
    *fetchPatientList({ payload }, { call, put }) {
      yield put({ type: 'setLoading', payload: true });
      try {
        const response = yield call(getPatientList, payload);
        if (response && response.code === 0) {
          yield put({ 
            type: 'setPatientList', 
            payload: {
              list: response.data.items,
              total: response.data.total
            } 
          });
          // 返回获取到的数据，以便 dispatch 的 Promise 解析为此值
          return {
            data: response.data.items,
            total: response.data.total,
            success: true,
          };
        } else {
          message.error(response?.message || '获取患者列表失败');
          // 返回错误时的结构
          return { data: [], total: 0, success: false };
        }
      } catch (error) {
        console.error('获取患者列表出错:', error);
        message.error('获取患者列表失败，请检查网络连接');
        // 返回异常时的结构
        return { data: [], total: 0, success: false };
      } finally {
        yield put({ type: 'setLoading', payload: false });
      }
    },

    *updatePatient({ payload }, { call, put }) {
      yield put({ type: 'setLoading', payload: true });
      try {
        const response = yield call(updatePatient, payload); // service中的updatePatient
        if (response && response.code === 0) {
          message.success('研究对象信息更新成功');
          // 可以在这里触发列表刷新等操作
          // yield put({ type: 'fetchPatientList', payload: {} }); // 例如：刷新列表
          return { success: true, data: response.data }; // 返回成功状态和数据
        } else {
          message.error(response?.message || '更新失败，请重试');
          return { success: false, message: response?.message || '更新失败，请重试' }; // 返回失败状态和消息
        }
      } catch (error) {
        console.error('更新研究对象出错:', error);
        message.error('更新失败，请检查网络连接');
        return { success: false, message: '更新失败，请检查网络连接' }; // 返回失败状态和消息
      } finally {
        yield put({ type: 'setLoading', payload: false });
      }
    },

    *uploadSignature({ payload }, { call, put }) {
      yield put({ type: 'setLoading', payload: true });
      try {
        const { file, patientId, role, cycleNumber } = payload;
        const response = yield call(uploadSignature, file, patientId, role, cycleNumber);
        if (response && response.code === 0) {
          message.success(role === 1 ? '医生签名上传成功' : '患者签名上传成功');
          return true;
        } else {
          message.error(response?.message || '签名上传失败，请重试');
          return false;
        }
      } catch (error) {
        console.error('签名上传出错:', error);
        message.error('签名上传失败，请检查网络连接');
        return false;
      } finally {
        yield put({ type: 'setLoading', payload: false });
      }
    },

    *generatePDF({ payload }, { call, put }) {
      yield put({ type: 'setLoading', payload: true });
      try {
        const { patientId } = payload;
        const response = yield call(generatePDF, patientId);
        if (response && response.code === 0) {
          message.success('PDF生成成功');
          return true;
        } else {
          message.error(response?.message || 'PDF生成失败，请重试');
          return false;
        }
      } catch (error) {
        console.error('PDF生成出错:', error);
        message.error('PDF生成失败，请检查网络连接');
        return false;
      } finally {
        yield put({ type: 'setLoading', payload: false });
      }
    },

    *previewPDF({ payload }, { call, put }) {
      yield put({ type: 'setLoading', payload: true });
      try {
        const response = yield call(previewPDF, payload);
        if (response && response.code === 0) {
          message.success('PDF预览加载成功');
          return {
            success: true,
            data: response.data,
          };
        } else {
          message.error(response?.message || 'PDF预览失败，请重试');
          return {
            success: false,
            message: response?.message || 'PDF预览失败，请重试',
          };
        }
      } catch (error) {
        console.error('PDF预览出错:', error);
        message.error('PDF预览失败，请检查网络连接');
        return {
          success: false,
          message: 'PDF预览失败，请检查网络连接',
        };
      } finally {
        yield put({ type: 'setLoading', payload: false });
      }
    },

    *fetchPatientIdByRecordId({ payload }, { call, put }) {
      yield put({ type: 'setLoading', payload: true });
      try {
        const response = yield call(findPatientIdByRecordId, payload);
        if (response && response.code === 0 && response.data !== null) {
          return {
            success: true,
            patientId: response.data,
          };
        } else {
          message.error(response?.message || '获取研究对象ID失败');
          return {
            success: false,
            patientId: null,
            message: response?.message || '获取研究对象ID失败',
          };
        }
      } catch (error) {
        console.error('获取研究对象ID出错:', error);
        message.error('获取研究对象ID失败，请检查网络连接');
        return {
          success: false,
          patientId: null,
          message: '获取研究对象ID失败，请检查网络连接',
        };
      } finally {
        yield put({ type: 'setLoading', payload: false });
      }
    },

    *fetchDemographicsEmoSymCorr({ payload }, { call, put }) {
      yield put({ type: 'setLoading', payload: true });
      try {
        const response = yield call(showDemographicsEmoSymCorr, payload);
        if (response && response.code === 0) {
          message.success('人口信息学数据获取成功');
          return {
            success: true,
            data: response.data,
          };
        } else {
          message.error(response?.message || '获取人口信息学数据失败');
          return {
            success: false,
            data: null,
            message: response?.message || '获取人口信息学数据失败',
          };
        }
      } catch (error) {
        console.error('获取人口信息学数据出错:', error);
        message.error('获取人口信息学数据失败，请检查网络连接');
        return {
          success: false,
          data: null,
          message: '获取人口信息学数据失败，请检查网络连接',
        };
      } finally {
        yield put({ type: 'setLoading', payload: false });
      }
    },

    *fetchDemographicsRadSymBur({ payload }, { call, put }) {
      yield put({ type: 'setLoading', payload: true });
      try {
        const response = yield call(showDemographicsRadSymBur, payload);
        if (response && response.code === 0) {
          message.success('人口信息学数据获取成功');
          return {
            success: true,
            data: response.data,
          };
        } else {
          message.error(response?.message || '获取人口信息学数据失败');
          return {
            success: false,
            data: null,
            message: response?.message || '获取人口信息学数据失败',
          };
        }
      } catch (error) {
        console.error('获取人口信息学数据出错:', error);
        message.error('获取人口信息学数据失败，请检查网络连接');
        return {
          success: false,
          data: null,
          message: '获取人口信息学数据失败，请检查网络连接',
        };
      } finally {
        yield put({ type: 'setLoading', payload: false });
      }
    },

    *fetchDiseaseInformationEmoSymCorr({ payload }, { call, put }) {
      yield put({ type: 'setLoading', payload: true });
      try {
        const response = yield call(showDiseaseInformationEmoSymCorr, payload);
        if (response && response.code === 0) {
          message.success('疾病信息数据获取成功');
          return {
            success: true,
            data: response.data,
          };
        } else {
          message.error(response?.message || '获取疾病信息数据失败');
          return {
            success: false,
            data: null,
            message: response?.message || '获取疾病信息数据失败',
          };
        }
      } catch (error) {
        console.error('获取疾病信息数据出错:', error);
        message.error('获取疾病信息数据失败，请检查网络连接');
        return {
          success: false,
          data: null,
          message: '获取疾病信息数据失败，请检查网络连接',
        };
      } finally {
        yield put({ type: 'setLoading', payload: false });
      }
    },

    *fetchDiseaseInformationRadSymBur({ payload }, { call, put }) {
      yield put({ type: 'setLoading', payload: true });
      try {
        const response = yield call(showDiseaseInformationRadSymBur, payload);
        if (response && response.code === 0) {
          message.success('疾病信息数据获取成功');
          return {
            success: true,
            data: response.data,
          };
        } else {
          message.error(response?.message || '获取疾病信息数据失败');
          return {
            success: false,
            data: null,
            message: response?.message || '获取疾病信息数据失败',
          };
        }
      } catch (error) {
        console.error('获取疾病信息数据出错:', error);
        message.error('获取疾病信息数据失败，请检查网络连接');
        return {
          success: false,
          data: null,
          message: '获取疾病信息数据失败，请检查网络连接',
        };
      } finally {
        yield put({ type: 'setLoading', payload: false });
      }
    },

    *updateDemographicsEmoSymCorr({ payload }, { call, put }) {
      yield put({ type: 'setLoading', payload: true });
      try {
        const response = yield call(updateDemographicsEmoSymCorr, payload);
        if (response && response.code === 0) {
          
          return {
            success: true,
            data: response.data,
          };
        } else {
          message.error(response?.message || '更新人口信息学数据失败');
          return {
            success: false,
            message: response?.message || '更新人口信息学数据失败',
          };
        }
      } catch (error) {
        console.error('更新人口信息学数据出错:', error);
        message.error('更新人口信息学数据失败，请检查网络连接');
        return {
          success: false,
          message: '更新人口信息学数据失败，请检查网络连接',
        };
      } finally {
        yield put({ type: 'setLoading', payload: false });
      }
    },

    *updateDiseaseInformationEmoSymCorr({ payload }, { call, put }) {
      yield put({ type: 'setLoading', payload: true });
      try {
        const response = yield call(updateDiseaseInformationEmoSymCorr, payload);
        if (response && response.code === 0) {
          
          return {
            success: true,
            data: response.data,
          };
        } else {
          message.error(response?.message || '更新疾病信息数据失败');
          return {
            success: false,
            message: response?.message || '更新疾病信息数据失败',
          };
        }
      } catch (error) {
        console.error('更新疾病信息数据出错:', error);
        message.error('更新疾病信息数据失败，请检查网络连接');
        return {
          success: false,
          message: '更新疾病信息数据失败，请检查网络连接',
        };
      } finally {
        yield put({ type: 'setLoading', payload: false });
      }
    },

    *updateDemographicsRadSymBur({ payload }, { call, put }) {
      yield put({ type: 'setLoading', payload: true });
      try {
        const response = yield call(updateDemographicsRadSymBur, payload);
        if (response && response.code === 0) {
          
          return {
            success: true,
            data: response.data,
          };
        } else {
          message.error(response?.message || '更新人口信息学数据失败');
          return {
            success: false,
            message: response?.message || '更新人口信息学数据失败',
          };
        }
      } catch (error) {
        console.error('更新人口信息学数据出错:', error);
        message.error('更新人口信息学数据失败，请检查网络连接');
        return {
          success: false,
          message: '更新人口信息学数据失败，请检查网络连接',
        };
      } finally {
        yield put({ type: 'setLoading', payload: false });
      }
    },

    *updateDiseaseInformationRadSymBur({ payload }, { call, put }) {
      yield put({ type: 'setLoading', payload: true });
      try {
        const response = yield call(updateDiseaseInformationRadSymBur, payload);
        if (response && response.code === 0) {
          
          return {
            success: true,
            data: response.data,
          };
        } else {
          message.error(response?.message || '更新疾病信息数据失败');
          return {
            success: false,
            message: response?.message || '更新疾病信息数据失败',
          };
        }
      } catch (error) {
        console.error('更新疾病信息数据出错:', error);
        message.error('更新疾病信息数据失败，请检查网络连接');
        return {
          success: false,
          message: '更新疾病信息数据失败，请检查网络连接',
        };
      } finally {
        yield put({ type: 'setLoading', payload: false });
      }
    },

    *fetchFieldHistory({ payload }, { call }) {
      try {
        const response = yield call(listHistory, payload);
        
        if (response && response.code === 0) {
          return {
            success: true,
            data: response.data,
          };
        } else {
          return {
            success: false,
            message: response?.message || '获取字段历史失败',
            data: [],
          };
        }
      } catch (error) {
        console.error('获取字段历史错误:', error);
        return {
          success: false,
          message: '获取字段历史失败',
          data: [],
        };
      }
    },

    *addCtcae({ payload }, { call, put }) {
      try {
        yield put({ type: 'setLoading', payload: true });
        const response = yield call(addCtcae, payload);
        
        if (response && response.code === 0) {
          return {
            success: true,
            message: response.message || '新增CTCAE记录成功',
            data: response.data,
          };
        } else {
          return {
            success: false,
            message: response?.message || '新增CTCAE记录失败',
            data: null,
          };
        }
      } catch (error) {
        console.error('新增CTCAE记录错误:', error);
        return {
          success: false,
          message: '新增CTCAE记录失败',
          data: null,
        };
      } finally {
        yield put({ type: 'setLoading', payload: false });
      }
    },

    *fetchCtcaeList({ payload }, { call, put }) {
      try {
        yield put({ type: 'setLoading', payload: true });
        const response = yield call(listCtcae, payload);
        
        if (response && response.code === 0) {
          return {
            success: true,
            message: response.message || '获取CTCAE记录成功',
            data: response.data,
          };
        } else {
          return {
            success: false,
            message: response?.message || '获取CTCAE记录失败',
            data: [],
          };
        }
      } catch (error) {
        console.error('获取CTCAE记录错误:', error);
        return {
          success: false,
          message: '获取CTCAE记录失败',
          data: [],
        };
      } finally {
        yield put({ type: 'setLoading', payload: false });
      }
    },

    *deletePatient({ payload }, { call, put }) {
      try {
        yield put({ type: 'setLoading', payload: true });
        const response = yield call(deletePatient, payload);
        
        if (response && response.code === 0) {
          return {
            success: true,
            message: response.message || '删除患者成功',
            data: response.data,
          };
        } else {
          return {
            success: false,
            message: response?.message || '删除患者失败',
            data: null,
          };
        }
      } catch (error) {
        console.error('删除患者错误:', error);
        return {
          success: false,
          message: '删除患者失败',
          data: null,
        };
      } finally {
        yield put({ type: 'setLoading', payload: false });
      }
    },

    *fetchScheduleList({ payload }, { call, put }) {
      try {
        yield put({ type: 'setLoading', payload: true });
        const response = yield call(getScheduleList, payload);
        
        if (response && response.code === 0) {
          return {
            success: true,
            message: response.message || '获取答题计划成功',
            data: response.data,
          };
        } else {
          return {
            success: false,
            message: response?.message || '获取答题计划失败',
            data: [],
          };
        }
      } catch (error) {
        console.error('获取答题计划错误:', error);
        return {
          success: false,
          message: '获取答题计划失败',
          data: [],
        };
      } finally {
        yield put({ type: 'setLoading', payload: false });
      }
    },

    *answerProjectEmo({ payload }, { call, put }) {
      try {
        yield put({ type: 'setLoading', payload: true });
        const response = yield call(answerProjectEmo, payload);
        
        if (response && response.code === 0) {
          message.success('问卷提交成功');
          return {
            success: true,
            message: response.message || '操作成功',
            data: response.data,
          };
        } else {
          message.error(response?.message || '问卷提交失败');
          return {
            success: false,
            message: response?.message || '问卷提交失败',
          };
        }
      } catch (error) {
        console.error('问卷提交错误:', error);
        message.error('问卷提交失败，请检查网络');
        return {
          success: false,
          message: '问卷提交失败，请检查网络',
        };
      } finally {
        yield put({ type: 'setLoading', payload: false });
      }
    },

    *fetchAnswerProjectEmo({ payload }, { call }) {
      try {
        const response = yield call(getAnswerProjectEmo, payload);
        if (response && response.code === 0) {
          return {
            success: true,
            data: response.data,
          };
        } else {
          message.error(response?.message || '获取答题详情失败');
          return {
            success: false,
            data: null,
          };
        }
      } catch (error) {
        console.error('获取答题详情错误:', error);
        message.error('获取答题详情失败，请检查网络');
        return {
          success: false,
          data: null,
        };
      }
    },

    *fetchExpiredSchedule({ payload }, { call, put }) {
      try {
        const response = yield call(getExpiredSchedule);
        if (response && response.code === 0) {
          yield put({ type: 'setExpiredSchedules', payload: response.data || [] });
          return { success: true, data: response.data };
        } else {
          message.error(response?.message || '获取未填列表失败');
          yield put({ type: 'setExpiredSchedules', payload: [] });
          return { success: false };
        }
      } catch (error) {
        console.error('获取未填列表错误:', error);
        message.error('获取未填列表失败');
        yield put({ type: 'setExpiredSchedules', payload: [] });
        return { success: false };
      }
    },
  },
  
  
  reducers: {
    setLoading(state, { payload }) {
      return {
        ...state,
        loading: payload,
      };
    },
    setPatientList(state, { payload }) {
      return {
        ...state,
        patientList: payload.list,
        total: payload.total,
      };
    },
    setCurrentDemographics(state, { payload }) {
      return { ...state, currentDemographics: payload };
    },
    setExpiredSchedules(state, { payload }) {
      return {
        ...state,
        expiredSchedules: payload,
      };
    },
  },
};

export default PatientsModel;