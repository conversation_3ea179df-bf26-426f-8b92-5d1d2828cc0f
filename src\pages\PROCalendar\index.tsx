import React, { useEffect, useState } from 'react';
import type { BadgeProps, CalendarProps } from 'antd';
import { Badge, Calendar, Col, Radio, Row, Select, Spin } from 'antd';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import localeData from 'dayjs/plugin/localeData';
import { connect, Dispatch } from 'umi';
import { PROCalendarModelState } from '@/models/PROCalendar';

dayjs.extend(localeData);
dayjs.locale('zh-cn');

const PROJECT_OPTIONS = [
    { value: 1, label: '基于电子化患者报告结局的肺癌患者情绪压力及症状负担的相关性研究' },
    { value: 2, label: '同济基于ePRO肺癌根治性同步放化疗患者症状负担和症状群研究项目' }
];

const getListData = (value: Dayjs, scheduleList: any[], projectId: number) => {
    const currentProjectName = PROJECT_OPTIONS.find(p => p.value === projectId)?.label;

    const listData = scheduleList
        .filter(item => {
            const itemDate = dayjs(item.planStartDate).format('YYYY-MM-DD');
            const valueDate = value.format('YYYY-MM-DD');
            return item.projectName === currentProjectName && itemDate === valueDate;
        })
        .map(item => {
            let type: BadgeProps['status'] = 'default';
            switch (item.statusName) {
                case '已过期':
                    type = 'error';
                    break;
                case '未答题':
                    type = 'warning';
                    break;
                case '已答题':
                    type = 'success';
                    break;
                default:
                    type = 'processing';
            }

            let cycleText = '';
            if (projectId === 1) {
                const cycleNumber = item.cycleNumber ?? 0;
                if (cycleNumber === 0) {
                    cycleText = '基线日期';
                } else {
                    const weekNumber = cycleNumber * 4;
                    const visitNumber = cycleNumber;
                    cycleText = `第${weekNumber}周的第${visitNumber}次随访`;
                }
            } else {
                cycleText = `周期 ${item.cycleNumber}`;
            }
            
            return {
                type,
                content: `患者ID ${item.patientId} - ${cycleText}: ${item.statusName}`
            };
        });

    return listData;
};

const getMonthData = (value: Dayjs, scheduleList: any[], projectId: number) => {
    const currentProjectName = PROJECT_OPTIONS.find(p => p.value === projectId)?.label;
    
    return scheduleList.filter(item => {
        const itemDate = dayjs(item.planStartDate).format('YYYY-MM');
        const valueDate = value.format('YYYY-MM');
        return item.projectName === currentProjectName && itemDate === valueDate;
    }).length;
};

interface PROCalendarProps {
    dispatch: Dispatch;
    proCalendar: PROCalendarModelState;
}

const PROCalendar: React.FC<PROCalendarProps> = ({ dispatch, proCalendar }) => {
    const { scheduleList, loading } = proCalendar;
    const [selectedProject, setSelectedProject] = useState(1);

    useEffect(() => {
        dispatch({
            type: 'proCalendar/fetchScheduleAll',
        });
    }, [dispatch]);

    const monthCellRender = (value: Dayjs) => {
        const num = getMonthData(value, scheduleList, selectedProject);
        return num ? (
            <div className="notes-month">
                <section>{num}</section>
                <span>个计划</span>
            </div>
        ) : null;
    };

    const dateCellRender = (value: Dayjs) => {
        const listData = getListData(value, scheduleList, selectedProject);
        return (
            <ul className="events">
                {listData.map((item) => (
                    <li key={item.content}>
                        <Badge status={item.type} text={item.content} />
                    </li>
                ))}
            </ul>
        );
    };

    const cellRender: CalendarProps<Dayjs>['cellRender'] = (current, info) => {
        if (info.type === 'date') return dateCellRender(current);
        if (info.type === 'month') return monthCellRender(current);
        return info.originNode;
    };

    const headerRender: CalendarProps<Dayjs>['headerRender'] = ({ value, type, onChange, onTypeChange }) => {
        const start = 0;
        const end = 12;
        const monthOptions: { label: string; value: number }[] = [];

        const localeData = dayjs.localeData();
        const months = localeData.monthsShort();

        for (let i = start; i < end; i++) {
            monthOptions.push({
                label: months[i],
                value: i,
            });
        }

        const year = value.year();
        const month = value.month();
        const yearOptions: { label: number; value: number }[] = [];
        for (let i = year - 10; i < year + 10; i += 1) {
            yearOptions.push({
                label: i,
                value: i,
            });
        }
        return (
            <div style={{ padding: '8px' }}>
                <Row gutter={8} justify="space-between" align="middle">
                    <Col>
                        <Select
                            value={selectedProject}
                            options={PROJECT_OPTIONS}
                            onChange={(newValue) => {
                                setSelectedProject(newValue);
                            }}
                            style={{ minWidth: '200px', maxWidth: '400px' }}
                            getPopupContainer={trigger => trigger.parentNode}
                        />
                    </Col>

                    <Col>
                        <Row gutter={8} align="middle">
                            <Col>
                                <Select
                                    size="small"
                                    popupMatchSelectWidth={false}
                                    value={year}
                                    options={yearOptions}
                                    onChange={(newYear) => {
                                        const now = value.clone().year(newYear);
                                        onChange(now);
                                    }}
                                    getPopupContainer={trigger => trigger.parentNode}
                                />
                            </Col>
                            <Col>
                                <Select
                                    size="small"
                                    popupMatchSelectWidth={false}
                                    value={month}
                                    options={monthOptions}
                                    onChange={(newMonth) => {
                                        const now = value.clone().month(newMonth);
                                        onChange(now);
                                    }}
                                    getPopupContainer={trigger => trigger.parentNode}
                                />
                            </Col>
                            <Col>
                                <Radio.Group
                                    size="small"
                                    onChange={(e) => onTypeChange(e.target.value)}
                                    value={type}
                                >
                                    <Radio.Button value="month">Month</Radio.Button>
                                    <Radio.Button value="year">Year</Radio.Button>
                                </Radio.Group>
                            </Col>
                        </Row>
                    </Col>
                </Row>
            </div>
        );
    };

    return (
      <Spin spinning={loading}>
        <Calendar cellRender={cellRender} headerRender={headerRender} />
      </Spin>
    );
};

const mapStateToProps = ({ proCalendar }: { proCalendar: PROCalendarModelState }) => ({
    proCalendar,
});

export default connect(mapStateToProps)(PROCalendar);