import React from 'react';
import { ProList } from '@ant-design/pro-components'
import { Typography } from 'antd';

const { Title } = Typography;

const dataSource = [
  {
    id: 1,
    name: 'dcf',
    phone: '13333333333',
    frequency: '第8周的第2次随访',
    warningTime: '2025-04-17 22:04:54',
    symptoms: [
      { name: '疼痛', score: 8 },
      { name: '疲乏', score: 8 },
      { name: '恶心', score: 8 },
      { name: '睡眠不安', score: 8 },
      { name: '苦恼', score: 8 },
      { name: '胃口差', score: 8 },
      { name: '昏昏欲睡', score: 7 },
      { name: '呕吐', score: 8 },
      { name: '便秘', score: 10 },
      { name: '一般活动', score: 8 },
    ],
    status: '待处理'
  },
  {
    id: 2,
    name: 'cfd',
    phone: '12222222222',
    frequency: '第8周的第2次随访',
    warningTime: '2025-04-17 22:04:54',
    symptoms: [
      { name: '疼痛', score: 8 },
      { name: '疲乏', score: 8 },
      { name: '恶心', score: 8 },
      { name: '睡眠不安', score: 8 },
      { name: '苦恼', score: 8 },
      { name: '胃口差', score: 8 },
      { name: '昏昏欲睡', score: 7 },
      { name: '呕吐', score: 8 },
      { name: '便秘', score: 10 },
      { name: '一般活动', score: 8 },
    ],
    status: '待处理'
  },
  {
    id: 3,
    name: 'fcd',
    phone: '16666666666',
    frequency: '第8周的第2次随访',
    warningTime: '2025-04-17 22:04:54',
    symptoms: [
      { name: '疼痛', score: 8 },
      { name: '疲乏', score: 8 },
      { name: '恶心', score: 8 },
      { name: '睡眠不安', score: 8 },
      { name: '苦恼', score: 8 },
      { name: '胃口差', score: 8 },
      { name: '昏昏欲睡', score: 7 },
      { name: '呕吐', score: 8 },
      { name: '便秘', score: 10 },
      { name: '一般活动', score: 8 },
    ],
    status: '待处理'
  },
  // You can keep other existing data or add more as needed
];

const Alter: React.FC = () => {
  return (
    <div style={{
      width: '100%',
      height: '100%',
      background: 'white',
      padding: 16,
      borderRadius: 8,
      boxShadow: '0 1px 2px 0 rgba(0,0,0,0.03)',
      WebkitUserSelect: 'none',
      MozUserSelect: 'none',
    }}>

      <Title level={5} style={{
        marginBottom: 16,
        paddingLeft: 32,
        color: '#39bbdb',
      }}>
        最近症状预警
      </Title>

      <ProList
        rowKey="id"
        headerTitle={null}
        dataSource={dataSource}
        style={{
          width: '100%',
          minHeight: '400',
          userSelect: 'none',
          pointerEvents: 'none', 
        }}
        pagination={{
          pageSize: 6,
          showSizeChanger: false,
          itemRender: (page, type, originalElement) =>
            React.cloneElement(originalElement, {
              style: {
                pointerEvents: 'auto',
                userSelect: 'text',
                cursor: 'pointer',
                ...(type === 'page' ? {
                  borderColor: '#39bbdb',
                } : {}),
              },
            }),
        }}
        showActions="hover"
        metas={{
          title: {
            dataIndex: 'name',
            render: (_, record) => (
              <div style={{ paddingLeft: 32 }}>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                  <span style={{ marginRight: 8, fontWeight: 'bold' }}>{record.name}</span>
                  <span style={{ color: '#727272', marginRight: 16 }}>电话：{record.phone}</span>
                  <span style={{ marginRight: 16 }}>{record.frequency}</span>
                  <span style={{ color: '#ff4d4f' }}>预警时间：{record.warningTime}</span>
                </div>
                <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>
                  {record.symptoms.map((symptom, index) => (
                    <span key={index} style={{ 
                      padding: '2px 8px', 
                    }}>
                      {symptom.name}:{symptom.score}
                    </span>
                  ))}
                </div>
              </div>
            ),
          },
          subTitle: {
            render: (_, record) => (
              <div style={{ paddingLeft: 32, marginTop: 8 }}>
                <span style={{ 
                  padding: '2px 8px', 
                  background: '#e6f7ff',
                  border: '1px solid #91d5ff',
                  borderRadius: 4,
                }}>
                  {record.status}
                </span>
              </div>
            ),
          }
        }}
        search={false}
        toolBarRender={false}
      />
    </div>
  );
};

export default Alter;