// src/CTCAEForm.tsx
import React, { useState, useEffect } from 'react';
import locale from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import { Form, Input, Select, Radio, DatePicker, ConfigProvider, AutoComplete, Typography, Button, message } from 'antd';
import 'dayjs/locale/zh-cn';
import { ctcaeSocData } from '../data/ctcaeSocData'; // 确保引入此文件
import { SOCData, AdverseEvent, AdverseEventGrade } from '../data/interfaces';

dayjs.locale('zh-cn');

const { Option } = Select;
const { TextArea } = Input;
const { Title, Paragraph, Text } = Typography;

interface SearchOption {
    value: string;
    label: string;
    socId: string;
    socName: string;
    adverseEvent: AdverseEvent;
}

interface CTCAEFormProps {
    patientId?: number; // 患者ID，用于提交表单时关联患者
    onSubmitSuccess?: () => void; // 提交成功后的回调函数
    dispatch?: any; // dispatch函数
    loading?: boolean; // loading状态
}

const CTCAEForm: React.FC<CTCAEFormProps> = ({ patientId, onSubmitSuccess, dispatch, loading = false }) => {
    const [form] = Form.useForm();
    
    const [selectedSocId, setSelectedSocId] = useState<string | null>(null);
    const [availableAdverseEvents, setAvailableAdverseEvents] = useState<AdverseEvent[]>([]);
    const [selectedAdverseEvent, setSelectedAdverseEvent] = useState<AdverseEvent | null>(null);
    const [selectedGrade, setSelectedGrade] = useState<string | null>(null);
    const [searchOptions, setSearchOptions] = useState<SearchOption[]>([]);
    const [allSearchOptions, setAllSearchOptions] = useState<SearchOption[]>([]);
    const [submitting, setSubmitting] = useState(false);

    // 初始化所有搜索选项
    useEffect(() => {
        const options: SearchOption[] = [];
        ctcaeSocData.forEach(soc => {
            soc.adverseEvents.forEach(ae => {
                options.push({
                    value: `${soc.socId}_${ae.socId}`,
                    label: `${ae.eventName} (${soc.socName})`,
                    socId: soc.socId,
                    socName: soc.socName,
                    adverseEvent: ae
                });
            });
        });
        setAllSearchOptions(options);
    }, []);

    useEffect(() => {
        if (selectedSocId) {
            const soc = ctcaeSocData.find(s => s.socId === selectedSocId);
            setAvailableAdverseEvents(soc ? soc.adverseEvents : []);
        } else {
            setAvailableAdverseEvents([]);
            setSelectedAdverseEvent(null);
            form.setFieldsValue({ adverseEvent: undefined, adverseEventGrade: undefined });
        }
    }, [selectedSocId, form]);

    const handleSocChange = (value: string) => {
        setSelectedSocId(value);
        setSelectedAdverseEvent(null);
        setSelectedGrade(null);
        form.setFieldsValue({ adverseEvent: undefined, adverseEventGrade: undefined });
    };

    const handleAdverseEventChange = (value: string) => {
        const ae = availableAdverseEvents.find(event => event.socId === value);
        setSelectedAdverseEvent(ae || null);
        setSelectedGrade(null);
        form.setFieldsValue({ adverseEventGrade: undefined });
    };
    
    const handleGradeChange = (e: any) => {
        setSelectedGrade(e.target.value);
    };

    // 处理模糊搜索
    const handleSearch = (searchText: string) => {
        if (!searchText) {
            setSearchOptions([]);
            return;
        }
        
        const filteredOptions = allSearchOptions.filter(option =>
            option.adverseEvent.eventName.toLowerCase().includes(searchText.toLowerCase())
        );
        setSearchOptions(filteredOptions);
    };

    // 处理模糊搜索选择
    const handleSearchSelect = (value: string) => {
        const selectedOption = allSearchOptions.find(option => option.value === value);
        if (selectedOption) {
            // 直接设置所有相关状态，不依赖useEffect
            const soc = ctcaeSocData.find(s => s.socId === selectedOption.socId);
            setAvailableAdverseEvents(soc ? soc.adverseEvents : []);
            setSelectedSocId(selectedOption.socId);
            setSelectedAdverseEvent(selectedOption.adverseEvent);
            setSelectedGrade(null);
            
            // 更新form字段
            form.setFieldsValue({
                soc: selectedOption.socId,
                adverseEvent: selectedOption.adverseEvent.socId,
                adverseEventGrade: undefined
            });
        }
        
        // 清空搜索框
        form.setFieldsValue({ adverseEventSearch: '' });
        setSearchOptions([]);
    };

    // 处理表单提交
    const handleSubmit = async () => {
        try {
            setSubmitting(true);
            
            // 表单验证
            const values = await form.validateFields();
            
            // 检查必要参数
            if (!patientId) {
                message.error('缺少患者ID，无法提交表单');
                return;
            }

            if (!selectedAdverseEvent) {
                message.error('请选择不良事件');
                return;
            }

            if (!selectedGrade) {
                message.error('请选择不良事件等级');
                return;
            }

            // 获取选中的SOC名称
            const selectedSoc = ctcaeSocData.find(soc => soc.socId === selectedSocId);
            if (!selectedSoc) {
                message.error('请选择系统器官分类');
                return;
            }

            // 构造提交数据
            const submitData = {
                patientId: patientId,
                enrollmentNumber: values.hospitalizationCount || 1,
                cycleNumber: getCycleNumber(values.frequency || '放疗基线'),
                occurrenceTime: values.aeOccurenceDate 
                    ? dayjs(values.aeOccurenceDate).format('YYYY-MM-DD HH:mm:ss')
                    : dayjs().format('YYYY-MM-DD HH:mm:ss'),
                soc: selectedSoc.socName,
                adverseEvent: selectedAdverseEvent.eventName,
                level: parseInt(selectedGrade),
                comment: values.objectiveData || '',
            };

            console.log('提交CTCAE数据:', submitData);

            // 调用API - 检查dispatch是否存在
            if (!dispatch) {
                message.error('系统错误：无法提交数据');
                return;
            }

            const result: any = await dispatch({
                type: 'patients/addCtcae',
                payload: submitData,
            });

            if (result && result.success) {
                message.success('CTCAE记录保存成功');
                form.resetFields();
                setSelectedSocId(null);
                setSelectedAdverseEvent(null);
                setSelectedGrade(null);
                
                // 调用成功回调
                if (onSubmitSuccess) {
                    onSubmitSuccess();
                }
            } else {
                message.error(result?.message || 'CTCAE记录保存失败');
            }
        } catch (error) {
            console.error('提交CTCAE表单错误:', error);
            message.error('表单验证失败，请检查输入');
        } finally {
            setSubmitting(false);
        }
    };

    // 将频次转换为周期编号
    const getCycleNumber = (frequency: string): number => {
        if (frequency === '放疗基线') return 0;
        
        const weekMatch = frequency.match(/放疗第(\d+)周/);
        if (weekMatch) {
            return parseInt(weekMatch[1]);
        }
        
        const afterWeekMatch = frequency.match(/放疗结束后(\d+)周/);
        if (afterWeekMatch) {
            return 11 + parseInt(afterWeekMatch[1]); // 假设放疗11周后开始计算
        }
        
        return 1; // 默认值
    };

    const renderGradeRadios = () => {
        if (!selectedAdverseEvent) {
            return <Text type="secondary">请先选择不良事件</Text>;
        }
        const gradesToShow: { value: string; label: string; description: string | null }[] = [];
        for (let i = 1; i <= 5; i++) {
            const gradeKey = `grade${i}` as keyof AdverseEventGrade;
            if (selectedAdverseEvent.grades[gradeKey] !== null && selectedAdverseEvent.grades[gradeKey] !== undefined) {
                 gradesToShow.push({
                    value: String(i),
                    label: `G${i}`,
                    description: selectedAdverseEvent.grades[gradeKey]
                });
            }
        }
         if (gradesToShow.length === 0) {
            return <Text type="secondary">该不良事件无可用分级标准。</Text>;
        }

        return (
            <Radio.Group onChange={handleGradeChange} value={selectedGrade} style={{ width: '100%' }}>
                {gradesToShow.map(g => (
                    <div key={g.value} style={{ marginBottom: '12px', width: '100%' }}>
                        <Radio value={g.value} style={{ display: 'flex', alignItems: 'flex-start' }}>
                            <div style={{ marginLeft: '8px', flex: 1 }}>
                                <Text strong>{g.label}: </Text>
                                <Text>{g.description}</Text>
                            </div>
                        </Radio>
                    </div>
                ))}
            </Radio.Group>
        );
    };

    return (
        <ConfigProvider
            locale={locale}
            theme={{
                components: {
                    Input: { activeBorderColor: '#39bbdb', hoverBorderColor: '#39bbdb', borderRadius: 4, activeShadow: '0 0 0 2px rgba(24, 144, 255, 0.2)' },
                    Select: { activeBorderColor: '#39bbdb', hoverBorderColor: '#39bbdb', borderRadius: 4, controlOutline: '0 0 0 2px rgba(24, 144, 255, 0.2)', optionSelectedBg: '#e6f7ff' },
                    DatePicker: { activeBorderColor: '#39bbdb', hoverBorderColor: '#39bbdb', borderRadius: 4, activeShadow: '0 0 0 2px rgba(24, 144, 255, 0.2)' },
                    Radio: { colorPrimary: '#39bbdb' },
                    Button: { colorPrimary: '#39bbdb' },
                },
            }}
        >
            <Form form={form} layout="vertical">
                <Title level={3}>CTCAE 不良事件记录</Title>
                
                <Form.Item 
                    label="入院次数" 
                    name="hospitalizationCount"
                    rules={[{ required: true, message: '请选择入院次数' }]}
                >
                    <Select placeholder="请选择">
                        {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(item => (
                            <Option key={item} value={item}>{item}</Option>
                        ))}
                    </Select>
                </Form.Item>

                <Form.Item 
                    label="频次" 
                    name="frequency"
                    rules={[{ required: true, message: '请选择频次' }]}
                >
                    <Select placeholder="请选择">
                        {[
                            '放疗基线', ...Array.from({ length: 11 }, (_, i) => `放疗第${i + 1}周`),
                            ...Array.from({ length: 11 }, (_, i) => `放疗结束后${i + 1}周`),
                            '放疗结束后4月上半月', '放疗结束后4月', '放疗结束后5月上半月', '放疗结束后5月',
                            '放疗结束后6月上半月', '放疗结束后6月',
                        ].map(item => (<Option key={item} value={item}>{item}</Option>))}
                    </Select>
                </Form.Item>

                <Form.Item 
                    label="不良事件发生日期" 
                    name="aeOccurenceDate"
                    rules={[{ required: true, message: '请选择不良事件发生日期' }]}
                >
                     <DatePicker style={{ width: '100%' }} placeholder="请选择日期" />
                </Form.Item>

                <Form.Item label="不良事件模糊查询" name="adverseEventSearch">
                    <AutoComplete
                        style={{ width: '100%' }}
                        placeholder="输入不良事件名称进行快速搜索..."
                        onSearch={handleSearch}
                        onSelect={handleSearchSelect}
                        options={searchOptions.map(option => ({
                            value: option.value,
                            label: option.label
                        }))}
                        filterOption={false}
                    />
                </Form.Item>

                <Form.Item 
                    label="系统器官分类(SOC)" 
                    name="soc"
                    rules={[{ required: true, message: '请选择系统器官分类' }]}
                >
                    <Select placeholder="请选择SOC" onChange={handleSocChange} allowClear>
                        {ctcaeSocData.map(soc => (
                            <Option key={soc.socId} value={soc.socId}>{soc.socName}</Option>
                        ))}
                    </Select>
                </Form.Item>

                <Form.Item 
                    label="不良事件" 
                    name="adverseEvent"
                    rules={[{ required: true, message: '请选择不良事件' }]}
                >
                    <Select placeholder="请选择不良事件" onChange={handleAdverseEventChange} disabled={!selectedSocId} allowClear>
                        {availableAdverseEvents.map(ae => (
                            <Option key={ae.socId} value={ae.socId}>{ae.eventName}</Option>
                        ))}
                    </Select>
                </Form.Item>
                
                <Form.Item 
                    label="不良事件评级" 
                    name="adverseEventGrade"
                    rules={[{ required: true, message: '请选择不良事件评级' }]}
                >
                   {renderGradeRadios()}
                </Form.Item>

                <Form.Item label="客观数据记录/当前级别定义" name="objectiveData">
                    <TextArea rows={3} placeholder="记录与不良事件相关的客观数据或手动输入分级定义"/>
                </Form.Item>

                <Form.Item>
                    <Button 
                        type="primary" 
                        onClick={handleSubmit}
                        loading={submitting || loading}
                        style={{ width: '100%' }}
                    >
                        保存CTCAE记录
                    </Button>
                </Form.Item>
            </Form>
        </ConfigProvider >
    );
};

export default CTCAEForm;