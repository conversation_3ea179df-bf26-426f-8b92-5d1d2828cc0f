// 同济基于ePRO肺癌根植性同步放化疗患者症状负担和症状群研究项目放化疗疾病信息表单
import React, { useEffect, useMemo, useImperativeHandle, forwardRef } from 'react';
import locale from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import { Form, Input, Select, Radio, InputNumber, DatePicker, Divider, ConfigProvider } from 'antd';
import 'dayjs/locale/zh-cn';
import FormItemWithHistory from '../History/FormItemWithHistory';

dayjs.locale('zh-cn');

const { Option } = Select;

interface RCDiseaseInfoProps {
  initialData?: any;
  onSubmit?: (formData: any) => Promise<boolean>;
  patientId?: number;
}

interface RCDiseaseInfoRef {
  submit: () => Promise<boolean>;
}

const RCDiseaseInfo = forwardRef<RCDiseaseInfoRef, RCDiseaseInfoProps>(({ 
  initialData, 
  onSubmit,
  patientId 
}, ref) => {
  const [form] = Form.useForm();

  // 映射后端数据到表单字段
  const formInitialValues = useMemo(() => {
    if (!initialData) return {};

    return {
      pathologicTyping: initialData.pathologicTyping,
      geneticMutations: initialData.geneticMutations,
      tumorSite: initialData.tumorSite,
      specificTumorSite: initialData.specificTumorSite,
      diagnosisDate: initialData.diagnosisDate ? dayjs(initialData.diagnosisDate) : null,
      tnm: initialData.tnm,
      tumorStage: initialData.tumorStage,
      mergeState: initialData.mergeState,
      mergeTumorName: initialData.mergeTumorName,
      treatment: initialData.treatment,
      chemotherapyCyclesPrior: initialData.chemotherapyCyclesPrior,
      immunotherapy: initialData.immunotherapyPrior || initialData.immunotherapy, // 兼容旧数据
      immunotherapyMaintenance: initialData.immunotherapyMaintenance,
      concurrentChemotherapy: initialData.concurrentChemotherapy,
      concurrentChemotherapyRegimen: initialData.concurrentChemotherapyRegimen,
      concurrentChemotherapyTime: initialData.concurrentChemotherapyTime,
      targetedMaintenance: initialData.targetedTherapyMaintenance,
      answerStatue: initialData.answerStatue || 0, // 默认未完成
    };
  }, [initialData]);

  useEffect(() => {
    if (initialData) {
      form.setFieldsValue(formInitialValues);
    }
  }, [form, formInitialValues, initialData]);

  // 处理表单提交
  const handleSubmit = async (): Promise<boolean> => {
    if (!patientId || !onSubmit) {
      return false;
    }

    try {
      const values = await form.validateFields();
      
      // 处理数据格式，转换成API需要的格式
      const submitData = {
        patientId: patientId,
        pathologicTyping: values.pathologicTyping,
        geneticMutations: values.geneticMutations,
        tumorSite: values.tumorSite,
        specificTumorSite: values.specificTumorSite,
        diagnosisDate: values.diagnosisDate ? values.diagnosisDate.format('YYYY-MM-DD') : undefined,
        TNM: values.tnm, // 注意这里的字段名转换，表单中是tnm，API要求TNM
        tumorStage: values.tumorStage,
        mergeState: values.mergeState,
        mergeTumorName: values.mergeTumorName,
        treatment: values.treatment,
        chemotherapyCyclesPrior: values.chemotherapyCyclesPrior,
        immunotherapyPrior: values.immunotherapy, // 表单字段名为immunotherapy，API为immunotherapyPrior
        immunotherapyMaintenance: values.immunotherapyMaintenance,
        concurrentChemotherapy: values.concurrentChemotherapy,
        concurrentChemotherapyRegimen: values.concurrentChemotherapyRegimen,
        concurrentChemotherapyTime: values.concurrentChemotherapyTime,
        targetedTherapyMaintenance: values.targetedMaintenance, // 表单字段名为targetedMaintenance，API为targetedTherapyMaintenance
        answerStatue: values.answerStatue,
      };

      return await onSubmit(submitData);
    } catch (error) {
      console.error('表单验证失败:', error);
      return false;
    }
  };

  // 使用useImperativeHandle暴露submit方法给父组件
  useImperativeHandle(ref, () => ({
    submit: handleSubmit,
  }));

  return (
    <ConfigProvider
      locale={locale}
      theme={{
        components: {
          Input: {
            activeBorderColor: '#39bbdb',
            hoverBorderColor: '#39bbdb',
            borderRadius: 4,
            activeShadow: '0 0 0 2px rgba(24, 144, 255, 0.2)',
          },
          Select: {
            activeBorderColor: '#39bbdb',
            hoverBorderColor: '#39bbdb',
            borderRadius: 4,
            controlOutline: '0 0 0 2px rgba(24, 144, 255, 0.2)',
            optionSelectedBg: '#e6f7ff',
          },
          InputNumber: {
            activeBorderColor: '#39bbdb',
            hoverBorderColor: '#39bbdb',
            borderRadius: 4,
            activeShadow: '0 0 0 2px rgba(24, 144, 255, 0.2)'
          },
          DatePicker: {
            activeBorderColor: '#39bbdb',
            hoverBorderColor: '#39bbdb',
            borderRadius: 4,
            activeShadow: '0 0 0 2px rgba(24, 144, 255, 0.2)'
          },
          Radio: {
            colorPrimary: '#39bbdb',
          },
        },
      }}
    >
      <Form layout="vertical" form={form} initialValues={formInitialValues}>
        <FormItemWithHistory
          name="pathologicTyping"
          label="病理分型"
          rules={[{ required: true, message: '请输入文本' }]}
          fieldName="pathologicTyping"
          fieldLabel="病理分型"
          patientId={patientId}
          project={3}
        >
          <Input placeholder="请输入文本" />
        </FormItemWithHistory>

        <FormItemWithHistory
          name="geneticMutations"
          label="基因突变"
          fieldName="geneticMutations"
          fieldLabel="基因突变"
          patientId={patientId}
          project={3}
        >
          <Input placeholder="请输入文本" />
        </FormItemWithHistory>

        <FormItemWithHistory
          name="tumorSite"
          label="肿瘤部位"
          fieldName="tumorSite"
          fieldLabel="肿瘤部位"
          patientId={patientId}
          project={3}
        >
          <Radio.Group>
            <Radio value={1}>左肺</Radio>
            <Radio value={2}>右肺</Radio>
          </Radio.Group>
        </FormItemWithHistory>

        <FormItemWithHistory
          name="specificTumorSite"
          label="肿瘤所在具体位置（可多选）"
          fieldName="specificTumorSite"
          fieldLabel="肿瘤所在具体位置"
          patientId={patientId}
          project={3}
        >
          <Select placeholder="请选择">
            <Option value={1}>上叶</Option>
            <Option value={2}>下叶</Option>
            <Option value={3}>中叶</Option>
          </Select>
        </FormItemWithHistory>

        <FormItemWithHistory
          name="diagnosisDate"
          label="疾病确诊日期"
          fieldName="diagnosisDate"
          fieldLabel="疾病确诊日期"
          patientId={patientId}
          project={3}
        >
          <DatePicker style={{ width: '100%' }} />
        </FormItemWithHistory>

        <FormItemWithHistory
          name="tnm"
          label="TNM分期"
          fieldName="tnm"
          fieldLabel="TNM分期"
          patientId={patientId}
          project={3}
        >
          <Input placeholder="请输入文本" />
        </FormItemWithHistory>

        <FormItemWithHistory
          name="tumorStage"
          label="肿瘤分期（可多选）"
          fieldName="tumorStage"
          fieldLabel="肿瘤分期"
          patientId={patientId}
          project={3}
        >
          <Select placeholder="请选择">
            <Option value={1}>I期</Option>
            <Option value={2}>II期</Option>
            <Option value={3}>III期</Option>
          </Select>
        </FormItemWithHistory>

        <FormItemWithHistory
          name="mergeState"
          label="是否合并其他肿瘤"
          fieldName="mergeState"
          fieldLabel="是否合并其他肿瘤"
          patientId={patientId}
          project={3}
        >
          <Radio.Group>
            <Radio value={0}>否</Radio>
            <Radio value={1}>是</Radio>
            <Radio value={2}>未知</Radio>
          </Radio.Group>
        </FormItemWithHistory>

        <FormItemWithHistory
          name="mergeTumorName"
          label="合并肿瘤名称"
          fieldName="mergeTumorName"
          fieldLabel="合并肿瘤名称"
          patientId={patientId}
          project={3}
        >
          <Input style={{ width: '100%' }} />
        </FormItemWithHistory>

        <FormItemWithHistory
          name="treatment"
          label="是否接受过手术治疗"
          fieldName="treatment"
          fieldLabel="是否接受过手术治疗"
          patientId={patientId}
          project={3}
        >
          <Radio.Group>
            <Radio value={0}>否</Radio>
            <Radio value={1}>是</Radio>
          </Radio.Group>
        </FormItemWithHistory>

        <FormItemWithHistory
          name="chemotherapyCyclesPrior"
          label="放疗前化疗周期"
          fieldName="chemotherapyCyclesPrior"
          fieldLabel="放疗前化疗周期"
          patientId={patientId}
          project={3}
        >
          <Input placeholder="请输入文本" />
        </FormItemWithHistory>

        <FormItemWithHistory
          name="immunotherapy"
          label="放疗前是否免疫治疗"
          fieldName="immunotherapy"
          fieldLabel="放疗前是否免疫治疗"
          patientId={patientId}
          project={3}
        >
          <Radio.Group>
            <Radio value={0}>否</Radio>
            <Radio value={1}>是</Radio>
          </Radio.Group>
        </FormItemWithHistory>

        <FormItemWithHistory
          name="immunotherapyMaintenance"
          label="放疗结束是否免疫维持治疗"
          fieldName="immunotherapyMaintenance"
          fieldLabel="放疗结束是否免疫维持治疗"
          patientId={patientId}
          project={3}
        >
          <Radio.Group>
            <Radio value={0}>否</Radio>
            <Radio value={1}>是</Radio>
          </Radio.Group>
        </FormItemWithHistory>

        <FormItemWithHistory
          name="concurrentChemotherapy"
          label="放疗期间是否同步化疗"
          fieldName="concurrentChemotherapy"
          fieldLabel="放疗期间是否同步化疗"
          patientId={patientId}
          project={3}
        >
          <Radio.Group>
            <Radio value={0}>否</Radio>
            <Radio value={1}>是</Radio>
          </Radio.Group>
        </FormItemWithHistory>

        <FormItemWithHistory
          name="concurrentChemotherapyRegimen"
          label="同步化疗方案"
          fieldName="concurrentChemotherapyRegimen"
          fieldLabel="同步化疗方案"
          patientId={patientId}
          project={3}
        >
          <Input style={{ width: '100%' }} />
        </FormItemWithHistory>

        <FormItemWithHistory
          name="concurrentChemotherapyTime"
          label="同步化疗时间"
          fieldName="concurrentChemotherapyTime"
          fieldLabel="同步化疗时间"
          patientId={patientId}
          project={3}
        >
          <Input style={{ width: '100%' }} />
        </FormItemWithHistory>

        <FormItemWithHistory
          name="targetedMaintenance"
          label="放疗结束后是否靶向维持治疗"
          fieldName="targetedMaintenance"
          fieldLabel="放疗结束后是否靶向维持治疗"
          patientId={patientId}
          project={3}
        >
          <Radio.Group>
            <Radio value={0}>否</Radio>
            <Radio value={1}>是</Radio>
          </Radio.Group>
        </FormItemWithHistory>

        <FormItemWithHistory
          name="answerStatue"
          fieldName="answerStatue"
          fieldLabel="答题状态"
          patientId={patientId}
          project={3}
        >
          <Select placeholder="请选择">
            <Option value={0}>未完成</Option>
            <Option value={1}>待检验</Option>
            <Option value={2}>已完成</Option>
          </Select>
        </FormItemWithHistory>
      </Form>
    </ConfigProvider>
  );
});

RCDiseaseInfo.displayName = 'RCDiseaseInfo';

export default RCDiseaseInfo;
