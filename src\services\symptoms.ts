import { request } from 'umi';
import { GetSymptomDataParams, GetSymptomDataResponse } from '@/pages/Symptoms/data';
import storeUtil from '@/utils/storeUtil';

/**
 * @description 获取特定患者的全部症状答题数据
 * @param params 包含 patientId 和 project
 * @returns Promise<GetSymptomDataResponse>
 */
export async function getSymptomDataByPatientId(params: GetSymptomDataParams): Promise<GetSymptomDataResponse> {
  const tokenData = storeUtil.get('token');
  const token = tokenData && tokenData.status === storeUtil.status.SUCCESS ? tokenData.value : null;

  return request(`${GLPT_API}/answer/getAnswerNumberByPatientId`, {
    method: 'GET',
    params,
    headers: {
      Authorization: `${token}`,
    },
  });
}
