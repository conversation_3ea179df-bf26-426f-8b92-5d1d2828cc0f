import React, { useEffect } from 'react';
import { But<PERSON>, Spin } from 'antd';
import { history, useLocation, connect, Dispatch } from 'umi';
import SymptomsCharts from '@/components/Symptoms/Charts/SymptomsCharts';
import { SymptomsModelState } from '@/models/symptoms';
import Top from '@/components/Symptoms/Top5';
import './styles.less';

interface SymptomsPageProps {
  dispatch: Dispatch;
  symptoms: SymptomsModelState;
}

const SymptomsPage: React.FC<SymptomsPageProps> = ({ dispatch, symptoms }) => {
    const location = useLocation();
    const { symptomData, loading } = symptoms;

    useEffect(() => {
        const query = new URLSearchParams(location.search);
        const patientId = query.get('patientId');
        const project = query.get('project');

        if (patientId && project) {
            dispatch({
                type: 'symptoms/fetchSymptomData',
                payload: {
                    patientId: Number(patientId),
                    project: Number(project),
                },
            });
        }
    }, [location, dispatch]);


    return (
        <div>
            <Button
                type="link"
                onClick={() => history.push('/patients')}
                style={{
                    marginBottom: 16,
                    paddingLeft: 0,
                    padding: '0 8px',
                    color: '#ffffff',
                    backgroundColor: '#39bbdb',
                    borderRadius: '4px',
                }}
            >
                &lt; 返回患者列表
            </Button>

            <Spin spinning={loading}>
                <div
                    style={{
                        backgroundColor: '#ffffff',
                        padding: '20px',
                        width: '100%',
                        maxWidth: '1760px',
                        margin: '0 auto',
                        boxSizing: 'border-box'
                    }}>
                    <SymptomsCharts rawData={symptomData} />
                </div>
            </Spin>

            <div
                style={{
                    padding: '20px',
                    width: '100%',
                    maxWidth: '1800px',
                    margin: '0 auto',
                    boxSizing: 'border-box'
                }}>
                <Top />
            </div>
        </div>

    );
}

const mapStateToProps = ({ symptoms }: { symptoms: SymptomsModelState }) => ({
    symptoms,
});

export default connect(mapStateToProps)(SymptomsPage);