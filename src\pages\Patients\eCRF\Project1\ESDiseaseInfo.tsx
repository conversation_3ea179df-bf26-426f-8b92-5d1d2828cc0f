// 基于电子化患者报告结局的晚期肺癌患者情绪压力及症状负担与脑转移状态相关性的研究放化疗疾病信息表单
import React, { useEffect, useMemo, useImperativeHandle, forwardRef } from 'react';
import locale from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import { Form, Input, Select, Radio, InputNumber, DatePicker, Divider, ConfigProvider } from 'antd';
import 'dayjs/locale/zh-cn';
import FormItemWithHistory from '../History/FormItemWithHistory';

dayjs.locale('zh-cn');

const { Option } = Select;

interface ESDiseaseInfoProps {
  initialData?: any;
  onSubmit?: (formData: any) => Promise<boolean>;
  patientId?: number;
}

interface ESDiseaseInfoRef {
  submit: () => Promise<boolean>;
}

const ESDiseaseInfo = forwardRef<ESDiseaseInfoRef, ESDiseaseInfoProps>(({ 
  initialData, 
  onSubmit,
  patientId 
}, ref) => {
    const [form] = Form.useForm();

    // 映射后端数据到表单字段
    const formInitialValues = useMemo(() => {
      if (!initialData) return {};

      return {
        diseaseName: initialData.diseaseName,
        diagnosisDate: initialData.diagnosisDate ? dayjs(initialData.diagnosisDate) : null,
        tnmtype: initialData.tnmtype,
        t: initialData.t,
        n: initialData.n,
        m: initialData.m,
        metastasis: initialData.metastasis,
        concomitantTumors: initialData.concomitantTumors,
        tumors: initialData.tumors,
        answerStatue: initialData.answerStatue,
      };
    }, [initialData]);

    useEffect(() => {
      if (initialData) {
        form.setFieldsValue(formInitialValues);
      }
    }, [form, formInitialValues, initialData]);

    // 处理表单提交
    const handleSubmit = async (): Promise<boolean> => {
      if (!patientId || !onSubmit) {
        return false;
      }

      try {
        const values = await form.validateFields();
        
        // 处理数据格式，转换成API需要的格式
        const submitData = {
          patientId: patientId,
          diseaseName: values.diseaseName,
          diagnosisDate: values.diagnosisDate ? values.diagnosisDate.format('YYYY-MM-DD') : undefined,
          tnmtype: values.tnmtype,
          t: values.t,
          n: values.n,
          m: values.m,
          metastasis: values.metastasis,
          concomitantTumors: values.concomitantTumors,
          tumors: values.tumors,
          answerStatue: values.answerStatue,
        };

        return await onSubmit(submitData);
      } catch (error) {
        console.error('表单验证失败:', error);
        return false;
      }
    };

    // 使用useImperativeHandle暴露submit方法给父组件
    useImperativeHandle(ref, () => ({
      submit: handleSubmit,
    }));

    return (
        <ConfigProvider
            locale={locale}
            theme={{
                components: {
                    Input: {
                        activeBorderColor: '#39bbdb',
                        hoverBorderColor: '#39bbdb',
                        borderRadius: 4,
                        activeShadow: '0 0 0 2px rgba(24, 144, 255, 0.2)',
                    },
                    Select: {
                        activeBorderColor: '#39bbdb',
                        hoverBorderColor: '#39bbdb',
                        borderRadius: 4,
                        controlOutline: '0 0 0 2px rgba(24, 144, 255, 0.2)',
                        optionSelectedBg: '#e6f7ff',
                    },
                    InputNumber: {
                        activeBorderColor: '#39bbdb',
                        hoverBorderColor: '#39bbdb',
                        borderRadius: 4,
                        activeShadow: '0 0 0 2px rgba(24, 144, 255, 0.2)'
                    },
                    DatePicker: {
                        activeBorderColor: '#39bbdb',
                        hoverBorderColor: '#39bbdb',
                        borderRadius: 4,
                        activeShadow: '0 0 0 2px rgba(24, 144, 255, 0.2)'
                    },
                    Radio: {
                        colorPrimary: '#39bbdb',
                    },
                },
            }}
        >
            <Form layout="vertical" form={form} initialValues={formInitialValues}>
                <FormItemWithHistory
                    name="diseaseName"
                    label="疾病名称"
                    rules={[{ required: true, message: '请输入文本' }]}
                    fieldName="diseaseName"
                    fieldLabel="疾病名称"
                    patientId={patientId}
                    project={2}
                >
                    <Input placeholder="请输入文本" />
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="diagnosisDate"
                    label="疾病确诊日期"
                    fieldName="diagnosisDate"
                    fieldLabel="疾病确诊日期"
                    patientId={patientId}
                    project={2}
                >
                    <DatePicker style={{ width: '100%' }} />
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="tnmtype"
                    label="TNM分期类型"
                    fieldName="tnmtype"
                    fieldLabel="TNM分期类型"
                    patientId={patientId}
                    project={2}
                >
                    <Radio.Group>
                        <Radio value={0}>pTNM</Radio>
                        <Radio value={1}>cTNM</Radio>
                        <Radio value={2}>无TNM分期</Radio>
                    </Radio.Group>
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="t"
                    label="T"
                    fieldName="t"
                    fieldLabel="T"
                    patientId={patientId}
                    project={2}
                >
                    <Select placeholder="请选择" allowClear>
                        <Option value={0}>Tx</Option>
                        <Option value={1}>T0</Option>
                        <Option value={2}>Tis</Option>
                        <Option value={3}>T1</Option>
                        <Option value={4}>T2</Option>
                        <Option value={5}>T3</Option>
                        <Option value={6}>T4</Option>
                    </Select>
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="n"
                    label="N"
                    fieldName="n"
                    fieldLabel="N"
                    patientId={patientId}
                    project={2}
                >
                    <Select placeholder="请选择" allowClear>
                        <Option value={0}>Nx</Option>
                        <Option value={1}>N0</Option>
                        <Option value={2}>N1</Option>
                        <Option value={3}>N2</Option>
                        <Option value={4}>N3</Option>
                    </Select>
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="m"
                    label="M"
                    fieldName="m"
                    fieldLabel="M"
                    patientId={patientId}
                    project={2}
                >
                    <Select placeholder="请选择" allowClear>
                        <Option value={0}>M0</Option>
                        <Option value={1}>M1</Option>
                        <Option value={2}>unknown</Option>
                    </Select>
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="metastasis"
                    label="是否发生远处转移"
                    fieldName="metastasis"
                    fieldLabel="是否发生远处转移"
                    patientId={patientId}
                    project={2}
                >
                    <Radio.Group>
                        <Radio value={0}>无转移</Radio>
                        <Radio value={1}>局部转移</Radio>
                        <Radio value={2}>远处转移</Radio>
                    </Radio.Group>
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="concomitantTumors"
                    label="是否合并其他肿瘤"
                    fieldName="concomitantTumors"
                    fieldLabel="是否合并其他肿瘤"
                    patientId={patientId}
                    project={2}
                >
                    <Radio.Group>
                        <Radio value={0}>否</Radio>
                        <Radio value={1}>是</Radio>
                        <Radio value={2}>未知</Radio>
                    </Radio.Group>
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="tumors"
                    label="合并肿瘤名称"
                    fieldName="tumors"
                    fieldLabel="合并肿瘤名称"
                    patientId={patientId}
                    project={2}
                >
                    <Input style={{ width: '100%' }} />
                </FormItemWithHistory>

                <FormItemWithHistory
                    name="answerStatue"
                    fieldName="answerStatue"
                    fieldLabel="答题状态"
                    patientId={patientId}
                    project={2}
                >
                    <Select placeholder="请选择">
                        <Option value={0}>未完成</Option>
                        <Option value={1}>待检验</Option>
                        <Option value={2}>已完成</Option>
                    </Select>
                </FormItemWithHistory>
            </Form>
        </ConfigProvider>
    );
});

ESDiseaseInfo.displayName = 'ESDiseaseInfo';

export default ESDiseaseInfo;
