import React from 'react';
import MultiLine<PERSON>hart from './MultiLineChart';
import { SymptomRecord } from '@/pages/Symptoms/data';
import { symptomNameMapping } from '@/utils/data';

interface SymptomsChartsProps {
  rawData: SymptomRecord[];
}

const SymptomsCharts: React.FC<SymptomsChartsProps> = ({ rawData }) => {
    if (!rawData || rawData.length === 0) {
        return <div style={{ textAlign: 'center', padding: '50px', color: '#999' }}>暂无症状数据</div>;
    }

    // Sort data by cycleNumber to ensure correct order
    const sortedData = [...rawData].sort((a, b) => a.cycleNumber - b.cycleNumber);

    const weekLabels = sortedData.map(item => `第${item.cycleNumber === 0 ? '基线' : item.cycleNumber}周期`);
    
    // Define the specific keys we want to display (q1 to q22)
    const symptomKeys = Array.from({ length: 22 }, (_, i) => `q${i + 1}`);

    const seriesData = symptomKeys.map(symptomKey => {
        return {
            name: symptomNameMapping[symptomKey] || symptomKey, // Use mapped name for legend
            data: sortedData.map(record => record[symptomKey] || 0) // Default to 0 if data is missing
        };
    });

  return (
    <div style={{ 
      padding: '20px', 
      width: '100%',
      maxWidth: '1800px', 
      margin: '0 auto',
      boxSizing: 'border-box'
    }}>
      <MultiLineChart 
        weekLabels={weekLabels} 
        seriesData={seriesData}
        height="450px"
      />
    </div>
  );
};

export default SymptomsCharts;