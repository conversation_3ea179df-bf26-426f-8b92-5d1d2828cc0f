import { EllipsisOutlined, PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable, TableDropdown } from '@ant-design/pro-components';
import { Button, ConfigProvider, message } from 'antd';
import { useRef, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import dayjs from 'dayjs';
import request from 'umi-request';
import NoFillToday from './NoFillToday';
import Statistic from './Statistic';
import PatientList from './PatientList/PatientList';

const Patients: React.FC = () => {
  return (
    <>
      <ConfigProvider
        theme={{
          components: {
            Button: {
              colorPrimary: '#39bbdb',
              colorPrimaryHover: '#2fa5c7',
              colorPrimaryActive: '#1e8bad',
            },
            Checkbox: {
              colorPrimary: '#39bbdb',
              colorPrimaryHover: '#39bbdb',
              colorBgContainer: '#fff',
              colorBorder: '#d9d9d9',
            },
          },
        }}
      >
        <div 
          style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '16px',
            padding: '16px',
            height: '100vh',
            boxSizing: 'border-box',
          }}
        >
          {/* 响应式统计卡片区域 */}
          <div 
            style={{
              display: 'flex',
              flexDirection: 'row',
              gap: '16px',
              flexWrap: 'wrap',
            }}
          >
            <div style={{ flex: '1 1 400px', minWidth: '300px' }}>
              <Statistic />
            </div>
            <div style={{ flex: '1 1 400px', minWidth: '300px' }}>
              <NoFillToday />
            </div>
          </div>

          {/* 患者列表区域 */}
          <div style={{ flex: 1, minHeight: '1000px', overflowY: 'auto' }}>
            <PatientList />
          </div>
        </div>
      </ConfigProvider>
    </>
  );
};

export default Patients;