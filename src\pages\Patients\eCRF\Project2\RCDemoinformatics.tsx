// 同济基于ePRO肺癌根植性同步放化疗患者症状负担和症状群研究项目放化疗人口信息学
import React, { useEffect, useMemo, useImperativeHandle, forwardRef } from 'react';
import locale from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import { Form, Input, Select, Radio, InputNumber, DatePicker, Divider, ConfigProvider } from 'antd';
import 'dayjs/locale/zh-cn';
import FormItemWithHistory from '../History/FormItemWithHistory';

dayjs.locale('zh-cn');

const { Option } = Select;

interface RCDemoinformaticsProps {
  initialData?: any;
  onSubmit?: (formData: any) => Promise<boolean>;
  patientId?: number;
}

interface RCDemoinformaticsRef {
  submit: () => Promise<boolean>;
}

const RCDemoinformatics = forwardRef<RCDemoinformaticsRef, RCDemoinformaticsProps>(({ 
  initialData, 
  onSubmit,
  patientId 
}, ref) => {
  const [form] = Form.useForm();

  // 计算BMI的函数
  const calculateBMI = (height: number, weight: number) => {
    if (height && weight && height > 0) {
      const heightInMeters = height / 100;
      return (weight / (heightInMeters * heightInMeters)).toFixed(2);
    }
    return '';
  };

  // 映射后端数据到表单字段
  const formInitialValues = useMemo(() => {
    if (!initialData) return {};

    return {
      name: initialData.name,
      gender: initialData.gender,
      birthDate: initialData.birthDate ? dayjs(initialData.birthDate) : null,
      illnessAge: initialData.illnessAge,
      ageRange: initialData.ageRange,
      smokeHistory: initialData.smokeHistory,
      smokingTime: initialData.smokingTime,
      drink: initialData.drink,
      drinkTime: initialData.drinkTime,
      ethnic: initialData.ethnic,
      otherEthnic: initialData.otherEthnic,
      maritalStatus: initialData.maritalStatus,
      degree: initialData.degree,
      height: initialData.height,
      weight: initialData.weight,
      bmi: initialData.bmi || calculateBMI(initialData.height, initialData.weight),
      workStatus: initialData.workStatus,
      career: initialData.career,
      insurance: initialData.insurance,
      hypertension: initialData.hypertension,
      hypertensionTime: initialData.hypertensionTime,
      diabetes: initialData.diabetes,
      diabetesTime: initialData.diabetesTime,
      otherDisease: initialData.otherDisease,
      otherDiseaseName: initialData.otherDiseaseName,
      concomitantDiseaseAmount: initialData.concomitantDiseaseAmount,
      answerStatue: initialData.answerStatue,
    };
  }, [initialData]);

  useEffect(() => {
    if (initialData) {
      form.setFieldsValue(formInitialValues);
    }
  }, [form, formInitialValues, initialData]);

  // 监听身高体重变化，自动计算BMI
  const handleHeightWeightChange = () => {
    const height = form.getFieldValue('height');
    const weight = form.getFieldValue('weight');
    if (height && weight) {
      const bmi = calculateBMI(height, weight);
      form.setFieldsValue({ bmi });
    }
  };

  // 处理表单提交
  const handleSubmit = async (): Promise<boolean> => {
    if (!patientId || !onSubmit) {
      return false;
    }

    try {
      const values = await form.validateFields();
      
      // 处理数据格式，转换成API需要的格式
      const submitData = {
        patientId: patientId,
        name: values.name,
        gender: values.gender,
        birthDate: values.birthDate ? values.birthDate.format('YYYY-MM-DD') : undefined,
        illnessAge: values.illnessAge,
        ageRange: values.ageRange,
        smokeHistory: values.smokeHistory,
        smokingTime: values.smokingTime,
        drink: values.drink,
        drinkTime: values.drinkTime,
        ethnic: values.ethnic,
        otherEthnic: values.otherEthnic,
        maritalStatus: values.maritalStatus,
        degree: values.degree,
        height: values.height,
        weight: values.weight,
        workStatus: values.workStatus,
        career: values.career,
        insurance: values.insurance,
        hypertension: values.hypertension,
        hypertensionTime: values.hypertensionTime,
        diabetes: values.diabetes,
        diabetesTime: values.diabetesTime,
        otherDisease: values.otherDisease,
        otherDiseaseName: values.otherDiseaseName,
        concomitantDiseaseAmount: values.concomitantDiseaseAmount,
        answerStatue: values.answerStatue,
      };

      return await onSubmit(submitData);
    } catch (error) {
      console.error('表单验证失败:', error);
      return false;
    }
  };

  // 使用useImperativeHandle暴露submit方法给父组件
  useImperativeHandle(ref, () => ({
    submit: handleSubmit,
  }));

  return (
    <ConfigProvider
      locale={locale}
      theme={{
        components: {
          Input: {
            activeBorderColor: '#39bbdb',
            hoverBorderColor: '#39bbdb',
            borderRadius: 4,
            activeShadow: '0 0 0 2px rgba(24, 144, 255, 0.2)',
          },
          Select: {
            activeBorderColor: '#39bbdb',
            hoverBorderColor: '#39bbdb',
            borderRadius: 4,
            controlOutline: '0 0 0 2px rgba(24, 144, 255, 0.2)',
            optionSelectedBg: '#e6f7ff',
          },
          InputNumber: {
            activeBorderColor: '#39bbdb',
            hoverBorderColor: '#39bbdb',
            borderRadius: 4,
            activeShadow: '0 0 0 2px rgba(24, 144, 255, 0.2)'
          },
          DatePicker: {
            activeBorderColor: '#39bbdb',
            hoverBorderColor: '#39bbdb',
            borderRadius: 4,
            activeShadow: '0 0 0 2px rgba(24, 144, 255, 0.2)'
          },
          Radio: {
            colorPrimary: '#39bbdb',
          },
        },
      }}
    >
      <Form layout="vertical" form={form} initialValues={formInitialValues}>
        <FormItemWithHistory
          name="name"
          label="姓名"
          rules={[{ required: true, message: '请输入姓名' }]}
          fieldName="name"
          fieldLabel="姓名"
          patientId={patientId}
          project={1}
        >
          <Input placeholder="请输入文本" />
        </FormItemWithHistory>

        <FormItemWithHistory
          name="gender"
          label="性别"
          rules={[{ required: true, message: '请输入性别' }]}
          fieldName="gender"
          fieldLabel="性别"
          patientId={patientId}
          project={1}
        >
          <Radio.Group>
            <Radio value={1}>男</Radio>
            <Radio value={2}>女</Radio>
          </Radio.Group>
        </FormItemWithHistory>

        <FormItemWithHistory
          name="birthDate"
          label="出生年月"
          fieldName="birthDate"
          fieldLabel="出生年月"
          patientId={patientId}
          project={1}
        >
          <DatePicker style={{ width: '100%' }} />
        </FormItemWithHistory>

        <FormItemWithHistory
          name="illnessAge"
          label="患病年龄"
          fieldName="illnessAge"
          fieldLabel="患病年龄"
          patientId={patientId}
          project={1}
        >
          <InputNumber min={0} style={{ width: '100%' }} />
        </FormItemWithHistory>

        <FormItemWithHistory
          name="ageRange"
          label="年龄"
          fieldName="ageRange"
          fieldLabel="年龄"
          patientId={patientId}
          project={1}
        >
          <Radio.Group>
            <Radio value={0}>≤65岁</Radio>
            <Radio value={1}>＞65岁</Radio>
          </Radio.Group>
        </FormItemWithHistory>

        <FormItemWithHistory
          name="smokeHistory"
          label="吸烟史"
          fieldName="smokeHistory"
          fieldLabel="吸烟史"
          patientId={patientId}
          project={1}
        >
          <Radio.Group>
            <Radio value={0}>目前抽烟</Radio>
            <Radio value={1}>已戒烟</Radio>
            <Radio value={2}>从不抽烟</Radio>
          </Radio.Group>
        </FormItemWithHistory>

        <FormItemWithHistory
          name="smokingTime"
          label="吸烟年数（年）"
          fieldName="smokingTime"
          fieldLabel="吸烟年数（年）"
          patientId={patientId}
          project={1}
        >
          <InputNumber min={0} style={{ width: '100%' }} />
        </FormItemWithHistory>

        <FormItemWithHistory
          name="drink"
          label="目前是否饮酒（持续饮酒1年以上）"
          fieldName="drink"
          fieldLabel="目前是否饮酒"
          patientId={patientId}
          project={1}
        >
          <Radio.Group>
            <Radio value={0}>否</Radio>
            <Radio value={1}>是</Radio>
            <Radio value={2}>已戒酒</Radio>
            <Radio value={3}>未知</Radio>
          </Radio.Group>
        </FormItemWithHistory>

        <FormItemWithHistory
          name="drinkTime"
          label="饮酒年数（年）"
          fieldName="drinkTime"
          fieldLabel="饮酒年数（年）"
          patientId={patientId}
          project={1}
        >
          <InputNumber min={0} style={{ width: '100%' }} />
        </FormItemWithHistory>

        <FormItemWithHistory
          name="ethnic"
          label="民族"
          fieldName="ethnic"
          fieldLabel="民族"
          patientId={patientId}
          project={1}
        >
          <Select placeholder="请选择">
            <Option value={0}>汉族</Option>
            <Option value={1}>土家族</Option>
            <Option value={2}>苗族</Option>
            <Option value={3}>彝族</Option>
            <Option value={4}>藏族</Option>
            <Option value={5}>羌族</Option>
            <Option value={6}>回族</Option>
            <Option value={7}>其它</Option>
            <Option value={8}>未知</Option>
          </Select>
        </FormItemWithHistory>

        <FormItemWithHistory
          name="otherEthnic"
          label="民族-其他"
          fieldName="otherEthnic"
          fieldLabel="民族-其他"
          patientId={patientId}
          project={1}
        >
          <Input placeholder="请输入其他民族" />
        </FormItemWithHistory>

        <FormItemWithHistory
          name="maritalStatus"
          label="婚姻状况"
          fieldName="maritalStatus"
          fieldLabel="婚姻状况"
          patientId={patientId}
          project={1}
        >
          <Select placeholder="请选择">
            <Option value={0}>未婚</Option>
            <Option value={1}>已婚</Option>
            <Option value={2}>离异</Option>
            <Option value={3}>丧偶</Option>
            <Option value={4}>分居</Option>
            <Option value={5}>其他</Option>
            <Option value={6}>未知</Option>
          </Select>
        </FormItemWithHistory>

        <FormItemWithHistory
          name="degree"
          label="学历"
          fieldName="degree"
          fieldLabel="学历"
          patientId={patientId}
          project={1}
        >
          <Select placeholder="请选择">
            <Option value={0}>文盲</Option>
            <Option value={1}>小学</Option>
            <Option value={2}>中学</Option>
            <Option value={3}>高中</Option>
            <Option value={4}>大专</Option>
            <Option value={5}>本科及以上</Option>
          </Select>
        </FormItemWithHistory>

        <FormItemWithHistory
          name="height"
          label="身高 (cm)"
          fieldName="height"
          fieldLabel="身高 (cm)"
          patientId={patientId}
          project={1}
        >
          <InputNumber 
            min={0} 
            style={{ width: '100%' }} 
            onChange={handleHeightWeightChange}
          />
        </FormItemWithHistory>

        <FormItemWithHistory
          name="weight"
          label="体重 (kg)"
          fieldName="weight"
          fieldLabel="体重 (kg)"
          patientId={patientId}
          project={1}
        >
          <InputNumber 
            min={0} 
            style={{ width: '100%' }} 
            onChange={handleHeightWeightChange}
          />
        </FormItemWithHistory>

        <Form.Item name="bmi" label="BMI体重指数">
          <Input placeholder="由其他字段计算得出" disabled />
        </Form.Item>

        <FormItemWithHistory
          name="workStatus"
          label="工作状态"
          fieldName="workStatus"
          fieldLabel="工作状态"
          patientId={patientId}
          project={1}
        >
          <Radio.Group>
            <Radio value={0}>工作</Radio>
            <Radio value={1}>退休</Radio>
            <Radio value={2}>失业</Radio>
          </Radio.Group>
        </FormItemWithHistory>

        <FormItemWithHistory
          name="career"
          label="职业"
          fieldName="career"
          fieldLabel="职业"
          patientId={patientId}
          project={1}
        >
          <Select placeholder="请选择">
            <Option value={0}>农民</Option>
            <Option value={2}>工人</Option>
            <Option value={3}>机关事业单位</Option>
            <Option value={4}>企业</Option>
            <Option value={5}>离退休</Option>
            <Option value={6}>其他</Option>
            <Option value={7}>无业</Option>
            <Option value={8}>未知</Option>
          </Select>
        </FormItemWithHistory>

        <FormItemWithHistory
          name="insurance"
          label="医疗保险类型（可多选）"
          fieldName="insurance"
          fieldLabel="医疗保险类型"
          patientId={patientId}
          project={1}
        >
          <Select placeholder="请选择">
            <Option value={0}>无</Option>
            <Option value={1}>城镇职工医保</Option>
            <Option value={2}>城镇居民医保</Option>
            <Option value={3}>农村居民医保</Option>
            <Option value={4}>商业保险</Option>
            <Option value={5}>公费医疗</Option>
            <Option value={6}>其他</Option>
            <Option value={7}>未知</Option>
          </Select>
        </FormItemWithHistory>

        <Divider />

        <FormItemWithHistory
          name="hypertension"
          label="是否有高血压？"
          fieldName="hypertension"
          fieldLabel="是否有高血压"
          patientId={patientId}
          project={1}
        >
          <Radio.Group>
            <Radio value={0}>否</Radio>
            <Radio value={1}>是</Radio>
          </Radio.Group>
        </FormItemWithHistory>

        <FormItemWithHistory
          name="hypertensionTime"
          label="高血压史年数（年）"
          fieldName="hypertensionTime"
          fieldLabel="高血压史年数（年）"
          patientId={patientId}
          project={1}
        >
          <InputNumber min={0} style={{ width: '100%' }} />
        </FormItemWithHistory>

        <FormItemWithHistory
          name="diabetes"
          label="是否有糖尿病？"
          fieldName="diabetes"
          fieldLabel="是否有糖尿病"
          patientId={patientId}
          project={1}
        >
          <Radio.Group>
            <Radio value={0}>否</Radio>
            <Radio value={1}>是</Radio>
          </Radio.Group>
        </FormItemWithHistory>

        <FormItemWithHistory
          name="diabetesTime"
          label="糖尿病史年数（年）"
          fieldName="diabetesTime"
          fieldLabel="糖尿病史年数（年）"
          patientId={patientId}
          project={1}
        >
          <InputNumber min={0} style={{ width: '100%' }} />
        </FormItemWithHistory>

        <FormItemWithHistory
          name="otherDisease"
          label="是否有其它疾病？"
          fieldName="otherDisease"
          fieldLabel="是否有其它疾病"
          patientId={patientId}
          project={1}
        >
          <Radio.Group>
            <Radio value={0}>否</Radio>
            <Radio value={1}>是</Radio>
          </Radio.Group>
        </FormItemWithHistory>

        <FormItemWithHistory
          name="otherDiseaseName"
          label="其它疾病名称"
          fieldName="otherDiseaseName"
          fieldLabel="其它疾病名称"
          patientId={patientId}
          project={1}
        >
          <Input placeholder="请输入文本" />
        </FormItemWithHistory>

        <FormItemWithHistory
          name="concomitantDiseaseAmount"
          label="伴随疾病数量"
          fieldName="concomitantDiseaseAmount"
          fieldLabel="伴随疾病数量"
          patientId={patientId}
          project={1}
        >
          <Radio.Group>
            <Radio value={0}>0</Radio>
            <Radio value={1}>1个以上</Radio>
          </Radio.Group>
        </FormItemWithHistory>
        
        <FormItemWithHistory
          name="answerStatue"
          fieldName="answerStatue"
          fieldLabel="答题状态"
          patientId={patientId}
          project={1}
        >
          <Select placeholder="请选择">
            <Option value={0}>未完成</Option>
            <Option value={1}>待检验</Option>
            <Option value={2}>已完成</Option>
          </Select>
        </FormItemWithHistory>
      </Form>
    </ConfigProvider>
  );
});

RCDemoinformatics.displayName = 'RCDemoinformatics';

export default RCDemoinformatics;
