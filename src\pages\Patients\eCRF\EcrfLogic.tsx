import React, { Component } from 'react';
import { connect } from 'umi';
import { Dispatch } from 'umi';
import { message } from 'antd';
import EcrfModal from './ECRFModal';
import './styles.less';

interface EcrfLogicProps {
  dispatch: Dispatch;
  loading?: boolean;
  visible: boolean;
  record: any;
  onClose: () => void;
}

interface EcrfLogicState {
  demographicsData: any;
  diseaseData: any;
  loading: boolean;
  activeTab: 'demographics' | 'disease';
}

class EcrfLogic extends Component<EcrfLogicProps, EcrfLogicState> {
  constructor(props: EcrfLogicProps) {
    super(props);
    this.state = {
      demographicsData: null,
      diseaseData: null,
      loading: false,
      activeTab: 'demographics',
    };
  }

  componentDidMount() {
    if (this.props.visible && this.props.record.id) {
      this.loadInitialData();
    }
  }

  componentDidUpdate(prevProps: EcrfLogicProps) {
    if (this.props.visible && !prevProps.visible && this.props.record.id) {
      this.loadInitialData();
    }
  }

  // 判断项目类型
  getProjectType = (projectName: string) => {
    if (projectName && projectName.includes('基于电子化患者报告结局的肺癌患者情绪压力及症状负担的相关性研究')) {
      return '1';
    } else if (projectName && projectName.includes('同济基于ePRO肺癌根治性同步放化疗患者症状负担和症状群研究项目')) {
      return '2';
    }
    return '1'; // 默认为项目1
  };

  // 加载初始数据
  loadInitialData = async () => {
    const { dispatch, record } = this.props;
    const currentProjectType = this.getProjectType(record.projectName);
    
    this.setState({ loading: true });

    try {
      // 并行加载人口信息学和疾病信息数据
      const [demographicsResult, diseaseResult] = await Promise.all([
        this.loadDemographicsData(currentProjectType, record.id, dispatch),
        this.loadDiseaseData(currentProjectType, record.id, dispatch)
      ]);

      this.setState({
        demographicsData: demographicsResult?.data || null,
        diseaseData: diseaseResult?.data || null,
      });
    } catch (error) {
      console.error('加载初始数据失败:', error);
      message.error('加载数据失败，请重试');
    } finally {
      this.setState({ loading: false });
    }
  };

  // 加载人口信息学数据
  loadDemographicsData = async (projectType: string, patientId: number, dispatch: Dispatch): Promise<any> => {
    try {
      if (projectType === '1') {
        const result = await dispatch({
          type: 'patients/fetchDemographicsEmoSymCorr',
          payload: { patientId },
        });
        return result;
      } else if (projectType === '2') {
        const result = await dispatch({
          type: 'patients/fetchDemographicsRadSymBur',
          payload: { patientId },
        });
        return result;
      }
      return null;
    } catch (error) {
      console.error('获取人口信息学数据失败:', error);
      return null;
    }
  };

  // 加载疾病信息数据
  loadDiseaseData = async (projectType: string, patientId: number, dispatch: Dispatch): Promise<any> => {
    try {
      if (projectType === '1') {
        const result = await dispatch({
          type: 'patients/fetchDiseaseInformationEmoSymCorr',
          payload: { patientId },
        });
        return result;
      } else if (projectType === '2') {
        const result = await dispatch({
          type: 'patients/fetchDiseaseInformationRadSymBur',
          payload: { patientId },
        });
        return result;
      }
      return null;
    } catch (error) {
      console.error('获取疾病信息数据失败:', error);
      return null;
    }
  };

  // 提交人口信息学数据
  handleDemographicsSubmit = async (formData: any): Promise<boolean> => {
    const { dispatch, record } = this.props;
    const currentProjectType = this.getProjectType(record.projectName);

    try {
      let updateResult;
      if (currentProjectType === '1') {
        updateResult = await dispatch({
          type: 'patients/updateDemographicsEmoSymCorr',
          payload: formData,
        });
      } else if (currentProjectType === '2') {
        updateResult = await dispatch({
          type: 'patients/updateDemographicsRadSymBur',
          payload: formData,
        });
      }

      return updateResult && updateResult.success;
    } catch (error) {
      console.error('提交人口信息学数据失败:', error);
      return false;
    }
  };

  // 提交疾病信息数据
  handleDiseaseSubmit = async (formData: any): Promise<boolean> => {
    const { dispatch, record } = this.props;
    const currentProjectType = this.getProjectType(record.projectName);

    try {
      let updateResult;
      if (currentProjectType === '1') {
        updateResult = await dispatch({
          type: 'patients/updateDiseaseInformationEmoSymCorr',
          payload: formData,
        });
      } else if (currentProjectType === '2') {
        updateResult = await dispatch({
          type: 'patients/updateDiseaseInformationRadSymBur',
          payload: formData,
        });
      }

      return updateResult && updateResult.success;
    } catch (error) {
      console.error('提交疾病信息数据失败:', error);
      return false;
    }
  };

  // 同时保存两个表单的数据
  handleSaveAll = async (demographicsFormData: any, diseaseFormData: any): Promise<boolean> => {
    this.setState({ loading: true });

    try {
      // 并行调用两个保存接口
      const [demographicsSuccess, diseaseSuccess] = await Promise.all([
        this.handleDemographicsSubmit(demographicsFormData),
        this.handleDiseaseSubmit(diseaseFormData)
      ]);

      if (demographicsSuccess && diseaseSuccess) {
        message.success('所有数据保存成功');
        this.props.onClose();
        return true;
      } else if (demographicsSuccess || diseaseSuccess) {
        message.error('请查看两个项目中是否有必填项目未填写！！！');
        return false;
      } else {
        message.error('数据保存失败，请重试');
        return false;
      }
    } catch (error) {
      console.error('保存数据时发生错误:', error);
      message.error('保存失败，请检查网络连接');
      return false;
    } finally {
      this.setState({ loading: false });
    }
  };

  // 切换标签页
  handleTabChange = (activeTab: 'demographics' | 'disease') => {
    this.setState({ activeTab });
  };

  render() {
    const { visible, record, onClose } = this.props;
    const { demographicsData, diseaseData, loading, activeTab } = this.state;
    const currentProjectType = this.getProjectType(record.projectName);

    return (
      <EcrfModal
        visible={visible}
        record={record}
        onClose={onClose}
        loading={loading}
        activeTab={activeTab}
        onTabChange={this.handleTabChange}
        demographicsData={demographicsData}
        diseaseData={diseaseData}
        projectType={currentProjectType}
        onDemographicsSubmit={this.handleDemographicsSubmit}
        onDiseaseSubmit={this.handleDiseaseSubmit}
        onSaveAll={this.handleSaveAll}
      />
    );
  }
}

const mapStateToProps = ({ patients, loading }: { patients: any; loading: any }) => {
  return {
    loading: loading.effects['patients/fetchDemographicsEmoSymCorr'] || 
             loading.effects['patients/fetchDemographicsRadSymBur'] ||
             loading.effects['patients/fetchDiseaseInformationEmoSymCorr'] ||
             loading.effects['patients/fetchDiseaseInformationRadSymBur'],
  };
};

export default connect(mapStateToProps)(EcrfLogic); 