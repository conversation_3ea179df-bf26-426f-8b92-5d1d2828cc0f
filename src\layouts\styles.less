.header-title {
  font-size: 22px;
  color: #39bbdb;
  line-height: 48px;
  font-weight: bold;
}

.header-container {
  display: flex;
  justify-content: center;
  width: 100%;
}

.centered-menu {
  border-bottom: none;
  line-height: 64px;
  background: transparent;
  display: flex;
  justify-content: center;
  margin: 0 auto;
}

.centered-menu > .ant-menu-item {
  margin: 0 16px;
  padding: 0 8px;
}

// 修改选中菜单项的样式
.ant-menu-horizontal {
  border-bottom: none;
  
  .ant-menu-item {
    color: rgba(0, 0, 0, 0.65);
    &:hover {
      color: #39bbdb !important;
    }
  }

  .ant-menu-item-selected {
    color: #39bbdb !important;
    background-color: transparent !important;
  }
}

.ant-menu-horizontal > .ant-menu-item::after,
.ant-menu-horizontal > .ant-menu-submenu::after {
  border-bottom: 2px solid transparent;
  bottom: -1px;
  transition: all 0.3s ease;
}

.ant-menu-horizontal > .ant-menu-item:hover::after {
  border-bottom-color: #39bbdb !important;
}

.ant-menu-horizontal > .ant-menu-item-selected::after {
  border-bottom: 3px solid #39bbdb !important;
}

.user-dropdown {
  position: absolute;
  right: 24px;
  top: 50%;
  transform: translateY(-50%);
}